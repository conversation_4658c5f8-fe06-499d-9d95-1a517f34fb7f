<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  
  <!-- Authentication Messages -->
  <data name="InvalidCredentials" xml:space="preserve">
    <value>Invalid credentials</value>
  </data>
  <data name="UserNotFound" xml:space="preserve">
    <value>User not found</value>
  </data>
  <data name="AccountDisabled" xml:space="preserve">
    <value>Account has been disabled</value>
  </data>
  <data name="LoginFailed" xml:space="preserve">
    <value>Login failed</value>
  </data>
  
  <!-- Ticket Messages -->
  <data name="TicketNotFound" xml:space="preserve">
    <value>Ticket not found</value>
  </data>
  <data name="CannotChangeClosedTicketStatus" xml:space="preserve">
    <value>Cannot change status of closed ticket</value>
  </data>
  <data name="RollbackChanceAlreadyUsed" xml:space="preserve">
    <value>Rollback chance has already been used</value>
  </data>
  
  <!-- General Error Messages -->
  <data name="UnauthorizedAccess" xml:space="preserve">
    <value>Unauthorized access</value>
  </data>
  <data name="InternalServerError" xml:space="preserve">
    <value>Internal server error</value>
  </data>
  <data name="BadRequest" xml:space="preserve">
    <value>Bad request</value>
  </data>
  
  <!-- Activity Log Messages -->
  <data name="ActivityLogExportFileName" xml:space="preserve">
    <value>ActivityLog_{0}.xlsx</value>
  </data>
  <data name="ActivityLogLoadFailed" xml:space="preserve">
    <value>Failed to load activity logs</value>
  </data>
  <data name="ActivityLogExportFailed" xml:space="preserve">
    <value>Failed to export activity logs</value>
  </data>
  <data name="ActivityLogNotFound" xml:space="preserve">
    <value>Activity log not found</value>
  </data>
  
  <!-- Activity Type Display Names -->
  <data name="ActivityTypeCreate" xml:space="preserve">
    <value>Create</value>
  </data>
  <data name="ActivityTypeUpdate" xml:space="preserve">
    <value>Update</value>
  </data>
  <data name="ActivityTypeDelete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="ActivityTypeAssign" xml:space="preserve">
    <value>Assign</value>
  </data>
  <data name="ActivityTypeUnassign" xml:space="preserve">
    <value>Unassign</value>
  </data>
  <data name="ActivityTypeMaintenance" xml:space="preserve">
    <value>Maintenance</value>
  </data>
  <data name="ActivityTypeDispose" xml:space="preserve">
    <value>Dispose</value>
  </data>
</root>
