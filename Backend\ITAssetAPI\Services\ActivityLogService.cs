using Microsoft.EntityFrameworkCore;
using ITAssetAPI.Data;
using ITAssetAPI.DTOs;
using ITAssetAPI.Models;
using System.Text.Json;

namespace ITAssetAPI.Services
{
    public class ActivityLogService : IActivityLogService
    {
        private readonly ApplicationDbContext _context;

        public ActivityLogService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<ActivityLogListResponseDto> GetActivityLogsAsync(int page, int limit, string? search = null, string? activityType = null, int? assetId = null, DateTime? startDate = null, DateTime? endDate = null)
        {
            Console.WriteLine($"=== GetActivityLogsAsync ===");
            Console.WriteLine($"Page: {page}, Limit: {limit}");
            Console.WriteLine($"Search: {search}");
            Console.WriteLine($"ActivityType: {activityType}");
            Console.WriteLine($"AssetId: {assetId}");
            Console.WriteLine($"StartDate: {startDate}");
            Console.WriteLine($"EndDate: {endDate}");
            
            var query = _context.ActivityLogs.AsQueryable();

            // 搜索过滤
            if (!string.IsNullOrEmpty(search))
            {
                var searchLower = search.ToLower();
                Console.WriteLine($"=== 搜索逻辑 ===");
                Console.WriteLine($"原始搜索词: '{search}'");
                Console.WriteLine($"小写搜索词: '{searchLower}'");

                // 检查是否是LOG前缀搜索（如：LOG、LOG000042、LOG42等）
                if (searchLower.StartsWith("log"))
                {
                    Console.WriteLine("检测到LOG前缀搜索");
                    // 提取LOG后面的数字部分
                    var numberPart = search.Substring(3); // 去掉"LOG"前缀
                    Console.WriteLine($"数字部分: '{numberPart}'");

                    if (int.TryParse(numberPart, out int logId))
                    {
                        // 精确ID匹配
                        Console.WriteLine($"精确ID匹配: {logId}");
                        query = query.Where(a => a.Id == logId);
                    }
                    else if (string.IsNullOrEmpty(numberPart))
                    {
                        // 只输入了"LOG"，搜索所有记录（因为所有记录都有LOG前缀）
                        Console.WriteLine("只输入LOG，显示所有记录");
                        // 不添加额外过滤条件
                    }
                    else
                    {
                        // LOG后面跟着非数字字符，按普通文本搜索
                        Console.WriteLine("LOG后跟非数字，按文本搜索");
                        query = query.Where(a =>
                            a.Description.Contains(search) ||
                            (a.AssetName != null && a.AssetName.Contains(search)) ||
                            (a.AssetNumber != null && a.AssetNumber.Contains(search)));
                    }
                }
                // 检查是否是纯数字搜索
                else if (int.TryParse(search, out int searchId))
                {
                    // 支持部分数字匹配：搜索ID包含该数字的记录
                    var searchString = searchId.ToString();
                    Console.WriteLine($"纯数字搜索: {search} -> 搜索ID包含'{searchString}'的记录");

                    query = query.Where(a =>
                        a.Id.ToString().Contains(searchString) ||
                        a.Description.Contains(search) ||
                        (a.AssetName != null && a.AssetName.Contains(search)) ||
                        (a.AssetNumber != null && a.AssetNumber.Contains(search)));
                }
                else
                {
                    // 普通文本搜索
                    Console.WriteLine("普通文本搜索");
                    query = query.Where(a =>
                        a.Description.Contains(search) ||
                        (a.AssetName != null && a.AssetName.Contains(search)) ||
                        (a.AssetNumber != null && a.AssetNumber.Contains(search)));
                }
            }

            // 活动类型过滤（支持多个类型，逗号分隔）
            if (!string.IsNullOrEmpty(activityType))
            {
                var activityTypes = activityType.Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(t => t.Trim())
                    .Where(t => Enum.TryParse<ActivityType>(t, true, out _))
                    .Select(t => Enum.Parse<ActivityType>(t, true))
                    .ToList();

                if (activityTypes.Any())
                {
                    Console.WriteLine($"活动类型筛选: {string.Join(", ", activityTypes)}");
                    query = query.Where(a => activityTypes.Contains(a.ActivityType));
                }
            }

            // 资产ID过滤
            if (assetId.HasValue)
            {
                query = query.Where(a => a.AssetId == assetId.Value);
            }

            // 日期范围过滤
            if (startDate.HasValue)
            {
                // 确保开始日期从当天的00:00:00开始，并转换为UTC
                var startOfDay = startDate.Value.Date;
                if (startOfDay.Kind == DateTimeKind.Unspecified)
                {
                    startOfDay = DateTime.SpecifyKind(startOfDay, DateTimeKind.Utc);
                }
                Console.WriteLine($"应用开始日期过滤: {startDate.Value} -> {startOfDay}");
                query = query.Where(a => a.CreatedAt >= startOfDay);
            }

            if (endDate.HasValue)
            {
                // 确保结束日期到当天的23:59:59结束，并转换为UTC
                var endOfDay = endDate.Value.Date.AddDays(1).AddTicks(-1);
                if (endOfDay.Kind == DateTimeKind.Unspecified)
                {
                    endOfDay = DateTime.SpecifyKind(endOfDay, DateTimeKind.Utc);
                }
                Console.WriteLine($"应用结束日期过滤: {endDate.Value} -> {endOfDay}");
                query = query.Where(a => a.CreatedAt <= endOfDay);
            }

            var totalCount = await query.CountAsync();
            var totalPages = (int)Math.Ceiling((double)totalCount / limit);

            var activityLogs = await query
                .OrderByDescending(a => a.CreatedAt)
                .Skip((page - 1) * limit)
                .Take(limit)
                .ToListAsync();

            // 手动映射到DTO，确保所有字段正确传递
            var activityLogDtos = activityLogs.Select(a => ActivityLogDto.FromModel(a)).ToList();

            Console.WriteLine($"Found {activityLogDtos.Count} activity logs");
            foreach (var dto in activityLogDtos)
            {
                Console.WriteLine($"ID: {dto.Id}, Type: {dto.ActivityType}, CreatedAt: {dto.CreatedAt}, Description: {dto.Description}");
            }

            return new ActivityLogListResponseDto
            {
                ActivityLogs = activityLogDtos,
                TotalCount = totalCount,
                Page = page,
                Limit = limit,
                TotalPages = totalPages
            };
        }

        public async Task<ActivityLogDto> CreateActivityLogAsync(CreateActivityLogDto createActivityLogDto)
        {
            // 将次要活动类型序列化为JSON字符串
            string? secondaryTypesJson = null;
            if (createActivityLogDto.SecondaryActivityTypes.Any())
            {
                secondaryTypesJson = JsonSerializer.Serialize(createActivityLogDto.SecondaryActivityTypes.Select(t => t.ToString()).ToList());
            }
            
            var activityLog = new ActivityLog
            {
                ActivityType = createActivityLogDto.ActivityType,
                SecondaryActivityTypes = secondaryTypesJson,
                Description = createActivityLogDto.Description,
                AssetId = createActivityLogDto.AssetId,
                UserId = createActivityLogDto.UserId,
                UserName = createActivityLogDto.UserName,
                AssetName = createActivityLogDto.AssetName,
                AssetNumber = createActivityLogDto.AssetNumber,
                OldValues = createActivityLogDto.OldValues,
                NewValues = createActivityLogDto.NewValues,
                ChangeSummary = createActivityLogDto.ChangeSummary,
                CreatedAt = DateTime.UtcNow
            };

            _context.ActivityLogs.Add(activityLog);
            await _context.SaveChangesAsync();

            return new ActivityLogDto
            {
                Id = activityLog.Id,
                ActivityType = activityLog.ActivityType,
                SecondaryActivityTypes = activityLog.SecondaryActivityTypes,
                Description = activityLog.Description,
                AssetId = activityLog.AssetId,
                UserId = activityLog.UserId,
                UserName = activityLog.UserName,
                AssetName = activityLog.AssetName,
                AssetNumber = activityLog.AssetNumber,
                OldValues = activityLog.OldValues,
                NewValues = activityLog.NewValues,
                ChangeSummary = activityLog.ChangeSummary,
                CreatedAt = activityLog.CreatedAt
            };
        }

        public async Task LogAssetActivityAsync(string activityType, string description, int? assetId, int userId, string userName, string? assetName = null, string? assetNumber = null, object? oldValues = null, object? newValues = null)
        {
            if (!Enum.TryParse<ActivityType>(activityType, true, out var activityTypeEnum))
            {
                throw new ArgumentException($"Invalid activity type: {activityType}");
            }

            var activityLog = new ActivityLog
            {
                ActivityType = activityTypeEnum,
                Description = description,
                AssetId = assetId,
                UserId = userId,
                UserName = userName,
                AssetName = assetName,
                AssetNumber = assetNumber,
                OldValues = oldValues != null ? JsonSerializer.Serialize(oldValues) : null,
                NewValues = newValues != null ? JsonSerializer.Serialize(newValues) : null,
                CreatedAt = DateTime.UtcNow
            };

            _context.ActivityLogs.Add(activityLog);
            await _context.SaveChangesAsync();
        }

        // 新增：创建组合活动日志
        public async Task LogCombinedAssetActivityAsync(
            ActivityType primaryType,
            List<ActivityType> secondaryTypes,
            int? assetId,
            int userId,
            string userName,
            string? assetName = null,
            string? assetNumber = null,
            object? oldValues = null,
            object? newValues = null)
        {
            Console.WriteLine($"=== LOGGING ACTIVITY ===");
            Console.WriteLine($"Primary Type: {primaryType}");
            Console.WriteLine($"Secondary Types: {string.Join(", ", secondaryTypes)}");
            Console.WriteLine($"Asset: {assetName} ({assetNumber})");
            
            // 生成详细描述
            var description = await GenerateSmartDescription(primaryType, secondaryTypes, assetName, assetNumber, oldValues, newValues);
            Console.WriteLine($"Description: {description}");
            
            // 生成变更摘要
            var changeSummary = GenerateChangeSummary(oldValues, newValues);
            Console.WriteLine($"Change Summary: {changeSummary}");
            
            // 将次要活动类型序列化为JSON字符串
            string? secondaryTypesJson = null;
            if (secondaryTypes.Any())
            {
                secondaryTypesJson = JsonSerializer.Serialize(secondaryTypes.Select(t => t.ToString()).ToList());
                Console.WriteLine($"Secondary Types JSON: {secondaryTypesJson}");
            }
            
            var activityLog = new ActivityLog
            {
                ActivityType = primaryType,
                SecondaryActivityTypes = secondaryTypesJson,
                Description = description,
                AssetId = assetId,
                UserId = userId,
                UserName = userName,
                AssetName = assetName,
                AssetNumber = assetNumber,
                OldValues = oldValues != null ? JsonSerializer.Serialize(oldValues) : null,
                NewValues = newValues != null ? JsonSerializer.Serialize(newValues) : null,
                ChangeSummary = changeSummary,
                CreatedAt = DateTime.UtcNow
            };

            _context.ActivityLogs.Add(activityLog);
            await _context.SaveChangesAsync();
            
            Console.WriteLine($"Activity log created successfully. ID: {activityLog.Id}");
        }

        // 新增：生成智能描述
        private async Task<string> GenerateSmartDescription(
            ActivityType primaryType,
            List<ActivityType> secondaryTypes,
            string? assetName,
            string? assetNumber,
            object? oldValues,
            object? newValues)
        {
            var assetInfo = !string.IsNullOrEmpty(assetName) ? $"{assetName} ({assetNumber})" : assetNumber ?? "资产";
            
            // 根据主要活动类型生成描述
            switch (primaryType)
            {
                case ActivityType.Create:
                    // 如果创建的同时分配给了用户，显示分配信息
                    if (secondaryTypes.Contains(ActivityType.Assign) && newValues != null)
                    {
                        Console.WriteLine($"=== CREATE ACTIVITY DEBUG ===");
                        Console.WriteLine($"newValues type: {newValues.GetType()}");
                        Console.WriteLine($"newValues content: {newValues}");
                        
                        try
                        {
                            var newDict = JsonSerializer.Deserialize<Dictionary<string, object>>(newValues.ToString()!);
                            var assignedTo = newDict!.ContainsKey("AssignedTo") ? 
                                newDict["AssignedTo"].ToString() : null;
                            var assignedUserId = newDict.ContainsKey("AssignedUserId") ? 
                                newDict["AssignedUserId"].ToString() : null;
                                
                            Console.WriteLine($"Parsed AssignedTo: '{assignedTo}'");
                            Console.WriteLine($"Parsed AssignedUserId: '{assignedUserId}'");
                                
                            // 如果AssignedTo为空但AssignedUserId有值，尝试从数据库查询用户名
                            if ((string.IsNullOrEmpty(assignedTo) || assignedTo == "null") && 
                                !string.IsNullOrEmpty(assignedUserId) && assignedUserId != "null" && assignedUserId != "0")
                            {
                                Console.WriteLine($"AssignedTo is empty, querying user by ID: {assignedUserId}");
                                try
                                {
                                    var userId = int.Parse(assignedUserId);
                                    var user = await _context.Users.FindAsync(userId);
                                    if (user != null)
                                    {
                                        assignedTo = user.Username;
                                        Console.WriteLine($"Found user: {assignedTo} for ID: {userId}");
                                    }
                                    else
                                    {
                                        Console.WriteLine($"User not found for ID: {userId}");
                                    }
                                }
                                catch (Exception ex) 
                                { 
                                    Console.WriteLine($"Error querying user: {ex.Message}");
                                }
                            }
                            else
                            {
                                Console.WriteLine($"Using existing AssignedTo: '{assignedTo}'");
                            }
                                
                            Console.WriteLine($"Final assignedTo: '{assignedTo}'");
                            if (!string.IsNullOrEmpty(assignedTo) && assignedTo != "null")
                            {
                                var result = $"创建了资产 {assetInfo} 并分配给 {assignedTo}";
                                Console.WriteLine($"Returning: {result}");
                                return result;
                            }
                            else
                            {
                                Console.WriteLine($"AssignedTo is empty or null, using default description");
                            }
                        }
                        catch (Exception ex) 
                        { 
                            Console.WriteLine($"Error parsing newValues: {ex.Message}");
                        }
                    }
                    else
                    {
                        Console.WriteLine($"No assign activity or newValues is null");
                    }
                    return $"创建了资产 {assetInfo}";
                
                case ActivityType.Update:
                    // 如果只是简单更新
                    if (secondaryTypes.Count == 0 || (secondaryTypes.Count == 1 && secondaryTypes[0] == ActivityType.Update))
                    {
                        var changeDetails = GenerateChangeDetails(oldValues, newValues);
                        if (!string.IsNullOrEmpty(changeDetails))
                        {
                            return $"更新了资产 {assetInfo}：{changeDetails}";
                        }
                        return $"更新了资产 {assetInfo}";
                    }
                    break;
                
                case ActivityType.Delete:
                    return $"删除了资产 {assetInfo}";
                
                case ActivityType.Assign:
                    // 显示分配给谁
                    if (newValues != null)
                    {
                        try
                        {
                            var newDict = JsonSerializer.Deserialize<Dictionary<string, object>>(newValues.ToString()!);
                            var assignedTo = newDict!.ContainsKey("AssignedTo") ? 
                                newDict["AssignedTo"].ToString() : null;
                            var assignedUserId = newDict.ContainsKey("AssignedUserId") ? 
                                newDict["AssignedUserId"].ToString() : null;
                                
                            // 如果AssignedTo为空但AssignedUserId有值，尝试从数据库查询用户名
                            if ((string.IsNullOrEmpty(assignedTo) || assignedTo == "null") && 
                                !string.IsNullOrEmpty(assignedUserId) && assignedUserId != "null" && assignedUserId != "0")
                            {
                                try
                                {
                                    var userId = int.Parse(assignedUserId);
                                    var user = await _context.Users.FindAsync(userId);
                                    if (user != null)
                                    {
                                        assignedTo = user.Username;
                                        Console.WriteLine($"Found user: {assignedTo} for ID: {userId}");
                                    }
                                    else
                                    {
                                        Console.WriteLine($"User not found for ID: {userId}");
                                    }
                                }
                                catch (Exception ex) 
                                { 
                                    Console.WriteLine($"Error querying user: {ex.Message}");
                                }
                            }
                                
                            if (!string.IsNullOrEmpty(assignedTo) && assignedTo != "null" && assignedTo != "")
                            {
                                if (secondaryTypes.Contains(ActivityType.Update))
                                {
                                    var changeDetails = GenerateChangeDetails(oldValues, newValues);
                                    if (!string.IsNullOrEmpty(changeDetails))
                                    {
                                        return $"资产 {assetInfo} 被分配给 {assignedTo} 并更新：{changeDetails}";
                                    }
                                    return $"资产 {assetInfo} 被分配给 {assignedTo} 并更新";
                                }
                                return $"资产 {assetInfo} 被分配给 {assignedTo}";
                            }
                        }
                        catch { /* 解析失败，使用默认描述 */ }
                    }
                    return $"资产 {assetInfo} 被分配";
                
                case ActivityType.Unassign:
                    // 显示从谁取消分配
                    if (oldValues != null)
                    {
                        try
                        {
                            var oldDict = JsonSerializer.Deserialize<Dictionary<string, object>>(oldValues.ToString()!);
                            var assignedTo = oldDict!.ContainsKey("AssignedTo") ? 
                                oldDict["AssignedTo"].ToString() : null;
                            var assignedUserId = oldDict.ContainsKey("AssignedUserId") ? 
                                oldDict["AssignedUserId"].ToString() : null;
                                
                            // 如果AssignedTo为空但AssignedUserId有值，尝试从数据库查询用户名
                            if ((string.IsNullOrEmpty(assignedTo) || assignedTo == "null") && 
                                !string.IsNullOrEmpty(assignedUserId) && assignedUserId != "null" && assignedUserId != "0")
                            {
                                try
                                {
                                    var userId = int.Parse(assignedUserId);
                                    var user = await _context.Users.FindAsync(userId);
                                    if (user != null)
                                    {
                                        assignedTo = user.Username;
                                        Console.WriteLine($"Found user: {assignedTo} for ID: {userId}");
                                    }
                                    else
                                    {
                                        Console.WriteLine($"User not found for ID: {userId}");
                                    }
                                }
                                catch (Exception ex) 
                                { 
                                    Console.WriteLine($"Error querying user: {ex.Message}");
                                }
                            }
                                
                            if (!string.IsNullOrEmpty(assignedTo) && assignedTo != "null" && assignedTo != "")
                            {
                                if (secondaryTypes.Contains(ActivityType.Update))
                                {
                                    var changeDetails = GenerateChangeDetails(oldValues, newValues);
                                    if (!string.IsNullOrEmpty(changeDetails))
                                    {
                                        return $"资产 {assetInfo} 从 {assignedTo} 取消分配并更新：{changeDetails}";
                                    }
                                    return $"资产 {assetInfo} 从 {assignedTo} 取消分配并更新";
                                }
                                return $"资产 {assetInfo} 从 {assignedTo} 取消分配";
                            }
                        }
                        catch { /* 解析失败，使用默认描述 */ }
                    }
                    return $"资产 {assetInfo} 取消分配";
                
                case ActivityType.Maintenance:
                    if (secondaryTypes.Contains(ActivityType.Update))
                    {
                        var changeDetails = GenerateChangeDetails(oldValues, newValues);
                        if (!string.IsNullOrEmpty(changeDetails))
                        {
                            return $"资产 {assetInfo} 设为维护状态并更新：{changeDetails}";
                        }
                        return $"资产 {assetInfo} 设为维护状态并更新";
                    }
                    return $"资产 {assetInfo} 设为维护状态";

                case ActivityType.Dispose:
                    if (secondaryTypes.Contains(ActivityType.Update))
                    {
                        var changeDetails = GenerateChangeDetails(oldValues, newValues);
                        if (!string.IsNullOrEmpty(changeDetails))
                        {
                            return $"资产 {assetInfo} 设为报废状态并更新：{changeDetails}";
                        }
                        return $"资产 {assetInfo} 设为报废状态并更新";
                    }
                    return $"资产 {assetInfo} 设为报废状态";
            }
            
            // 默认组合描述方式
            var actions = new List<string>();
            
            // 添加主要活动
            actions.Add(GetActivityTypeDisplayName(primaryType));
            
            // 添加次要活动（去重）
            var uniqueSecondaryTypes = secondaryTypes.Distinct().ToList();
            foreach (var secondaryType in uniqueSecondaryTypes)
            {
                if (secondaryType != primaryType) // 避免重复
                {
                    actions.Add(GetActivityTypeDisplayName(secondaryType));
                }
            }
            
            var actionText = string.Join("和", actions);
            
            // 生成具体变更描述
            var changes = GenerateChangeDetails(oldValues, newValues);
            
            if (!string.IsNullOrEmpty(changes))
            {
                return $"资产 {assetInfo} {actionText}：{changes}";
            }
            
            return $"资产 {assetInfo} {actionText}";
        }

        // 新增：生成变更详情
        private string GenerateChangeDetails(object? oldValues, object? newValues)
        {
            if (oldValues == null || newValues == null) return string.Empty;
            
            try
            {
                var oldDict = JsonSerializer.Deserialize<Dictionary<string, object>>(oldValues.ToString()!);
                var newDict = JsonSerializer.Deserialize<Dictionary<string, object>>(newValues.ToString()!);
                
                var changes = new List<string>();
                
                foreach (var key in newDict.Keys)
                {
                    if (oldDict.ContainsKey(key) && !Equals(oldDict[key], newDict[key]))
                    {
                        var fieldName = GetFieldDisplayName(key);
                        var oldValue = GetDisplayValue(oldDict[key]);
                        var newValue = GetDisplayValue(newDict[key]);
                        
                        if (oldValue != newValue)
                        {
                            changes.Add($"{fieldName} {oldValue} → {newValue}");
                        }
                    }
                }
                
                return string.Join("，", changes);
            }
            catch
            {
                return string.Empty;
            }
        }

        // 新增：生成变更摘要
        private string GenerateChangeSummary(object? oldValues, object? newValues)
        {
            if (oldValues == null || newValues == null) return string.Empty;
            
            try
            {
                var oldDict = JsonSerializer.Deserialize<Dictionary<string, object>>(oldValues.ToString()!);
                var newDict = JsonSerializer.Deserialize<Dictionary<string, object>>(newValues.ToString()!);
                
                var keyChanges = new List<string>();
                
                // 检查关键字段的变更
                var keyFields = new[] { "Status", "AssignedTo", "Category" };
                
                foreach (var field in keyFields)
                {
                    if (oldDict.ContainsKey(field) && newDict.ContainsKey(field) && 
                        !Equals(oldDict[field], newDict[field]))
                    {
                        var oldValue = GetDisplayValue(oldDict[field]);
                        var newValue = GetDisplayValue(newDict[field]);
                        keyChanges.Add($"{oldValue} → {newValue}");
                    }
                }
                
                return string.Join("，", keyChanges);
            }
            catch
            {
                return string.Empty;
            }
        }

        // 新增：获取字段显示名称
        private string GetFieldDisplayName(string fieldName)
        {
            return fieldName switch
            {
                "Status" => "状态",
                "AssignedTo" => "分配给",
                "Category" => "分类",
                "Name" => "名称",
                "Brand" => "品牌",
                "Model" => "型号",
                "Location" => "位置",
                "Value" => "价值",
                "Description" => "描述",
                _ => fieldName
            };
        }

        // 新增：获取显示值
        private string GetDisplayValue(object? value)
        {
            if (value == null) return "无";
            
            var stringValue = value.ToString();
            
            // 处理状态枚举
            if (Enum.TryParse<AssetStatus>(stringValue, out var status))
            {
                return status switch
                {
                    AssetStatus.Available => "可用",
                    AssetStatus.Assigned => "已分配",
                    AssetStatus.Maintenance => "维护中",
                    AssetStatus.Retired => "已报废",
                    _ => stringValue
                };
            }
            
            // 处理分类枚举
            if (Enum.TryParse<AssetCategory>(stringValue, out var category))
            {
                return category switch
                {
                    AssetCategory.Laptop => "笔记本电脑",
                    AssetCategory.Desktop => "台式电脑",
                    AssetCategory.Monitor => "显示器",
                    AssetCategory.Printer => "打印机",
                    AssetCategory.Phone => "电话",
                    AssetCategory.Tablet => "平板电脑",
                    AssetCategory.Server => "服务器",
                    AssetCategory.Network => "网络设备",
                    AssetCategory.Other => "其他",
                    _ => stringValue
                };
            }
            
            return stringValue ?? "无";
        }

        // 新增：获取活动类型显示名称
        private string GetActivityTypeDisplayName(ActivityType type)
        {
            return type switch
            {
                ActivityType.Create => "创建",
                ActivityType.Update => "更新",
                ActivityType.Delete => "删除",
                ActivityType.Assign => "分配",
                ActivityType.Unassign => "取消分配",
                ActivityType.Maintenance => "设为维护",
                ActivityType.Dispose => "设为报废",
                _ => type.ToString()
            };
        }
    }
} 