import 'dart:async';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../models/user.dart';
import '../services/user_service.dart';
import '../services/excel_service.dart';
import '../widgets/loading_widget.dart';
import '../widgets/error_widget.dart';
import '../widgets/main_layout.dart';
import '../widgets/animated_search_bar.dart';
import '../widgets/success_animation.dart';
import '../providers/auth_provider.dart';
import '../config/routes.dart';
import 'user_form_screen.dart';
import 'user_detail_screen.dart';
import '../utils/timezone_utils.dart';

class UserManagementScreen extends StatefulWidget {
  const UserManagementScreen({Key? key}) : super(key: key);

  @override
  State<UserManagementScreen> createState() => _UserManagementScreenState();
}

class _UserManagementScreenState extends State<UserManagementScreen> {
  final UserService _userService = UserService();
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  Timer? _debounceTimer;
  
  UserListResponse? _userListResponse;
  bool _isLoading = false;
  String? _error;
  bool _showScrollToTop = false;
  bool _isSearching = false;
  bool _searchCompleted = false;
  
  // Filter and pagination state
  List<String> _selectedRoles = []; // 改为列表支持多选
  List<String> _selectedDepartments = []; // 改为列表支持多选
  List<String> _appliedRoles = []; // 实际应用的角色筛选列表
  List<String> _appliedDepartments = []; // 实际应用的部门筛选列表
  int _currentPage = 1;
  final int _pageSize = 10;
  String _sortBy = 'CreatedAt';
  bool _sortDescending = true;
  
  List<String> _roles = [];
  List<String> _departments = [];

  @override
  void initState() {
    super.initState();
    // 检查认证状态
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAuthAndInitialize();
    });
    _scrollController.addListener(_onScroll);
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  Future<void> _checkAuthAndInitialize() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    
    // 如果用户未登录，重新初始化认证状态
    if (!authProvider.isLoggedIn) {
      await authProvider.initializeAuth();
    }
    
    // 如果仍然未登录，显示错误信息
    if (!authProvider.isLoggedIn) {
      setState(() {
        _error = '用户未登录，请重新登录';
      });
      return;
    }
    
    // 加载初始数据
    await _loadInitialData();
  }

  Future<void> _loadInitialData() async {
    await Future.wait([
      _loadUsers(),
      _loadRoles(),
      _loadDepartments(),
    ]);
  }

  Future<void> _loadUsers() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final query = UserQuery(
        search: _searchController.text.isEmpty ? null : _searchController.text,
        roles: _appliedRoles.isNotEmpty ? _appliedRoles : null, // 使用多选角色
        departments: _appliedDepartments.isNotEmpty ? _appliedDepartments : null, // 使用多选部门
        pageNumber: _currentPage,
        pageSize: _pageSize,
        sortBy: _sortBy,
        sortDescending: _sortDescending,
      );

      final response = await _userService.getUsers(query);
      
      setState(() {
        _userListResponse = response;
        _isLoading = false;
        _searchCompleted = true;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _loadRoles() async {
    try {
      final roles = await _userService.getRoles();
      setState(() {
        _roles = roles;
      });
    } catch (e) {
      // Handle error silently for roles
    }
  }

  Future<void> _loadDepartments() async {
    try {
      final departments = await _userService.getDepartments();
      setState(() {
        _departments = departments;
      });
    } catch (e) {
      // Handle error silently for departments
    }
  }

  void _onSearchChanged() {
    if (_debounceTimer?.isActive ?? false) _debounceTimer!.cancel();

    // 立即更新搜索状态
    setState(() {
      _isSearching = _searchController.text.isNotEmpty;
      _searchCompleted = false;
    });

    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      _currentPage = 1;
      _loadUsers();
    });
  }

  void _onFilterChanged() {
    // 应用筛选时，更新已应用的筛选条件
    setState(() {
      _appliedRoles = List.from(_selectedRoles);
      _appliedDepartments = List.from(_selectedDepartments);
    });
    _currentPage = 1;
    _loadUsers();
  }

  void _onPageChanged(int page) {
    setState(() {
      _currentPage = page;
    });
    _loadUsers();
  }

  void _onSortChanged(String sortBy) {
    setState(() {
      if (_sortBy == sortBy) {
        _sortDescending = !_sortDescending;
      } else {
        _sortBy = sortBy;
        _sortDescending = true;
      }
    });
    _loadUsers();
  }

  void _onScroll() {
    if (_scrollController.offset >= 400 && !_showScrollToTop) {
      setState(() {
        _showScrollToTop = true;
      });
    } else if (_scrollController.offset < 400 && _showScrollToTop) {
      setState(() {
        _showScrollToTop = false;
      });
    }
  }

  void _scrollToTop() {
    _scrollController.animateTo(
      0,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  void _clearFilters() {
    setState(() {
      _searchController.clear();
      _selectedRoles = [];
      _selectedDepartments = [];
      _appliedRoles = [];
      _appliedDepartments = [];
      _isSearching = false;
      _searchCompleted = false;
    });
    _currentPage = 1;
    _loadUsers();
  }

  // 检查是否有已应用的筛选条件
  bool get _hasActiveFilters {
    return (_searchController.text.isNotEmpty && _searchCompleted) ||
           _appliedRoles.isNotEmpty ||
           _appliedDepartments.isNotEmpty;
  }

  Future<void> _deleteUser(User user) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除用户 "${user.username}" 吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _userService.deleteUser(user.id);
        
        // 显示现代化的成功动画
        SuccessAnimationOverlay.show(
          context,
          title: '删除成功',
          message: '用户 "${user.username}" 已成功删除',
          onComplete: () {
            _loadUsers();
          },
        );
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('删除失败: $e')),
        );
      }
    }
  }

  void _navigateToUserForm({User? user}) async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => UserFormScreen(user: user),
      ),
    );

    if (result == true) {
      _loadUsers();
    }
  }

  void _navigateToUserDetail(User user) async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => UserDetailScreen(user: user),
      ),
    );

    if (result == true) {
      _loadUsers();
    }
  }

  Future<void> _showChangeRoleDialog(User user) async {
    String? selectedRole = user.role;
    
    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('更改用户权限 - ${user.username}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('当前权限: ${user.role}'),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: selectedRole,
              decoration: const InputDecoration(
                labelText: '选择新权限',
                border: OutlineInputBorder(),
              ),
              items: UserRole.all.map((role) => DropdownMenuItem(
                value: role,
                child: Text(role),
              )).toList(),
              onChanged: (value) {
                selectedRole = value;
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(selectedRole),
            child: const Text('确认'),
          ),
        ],
      ),
    );

    if (result != null && result != user.role) {
      await _changeUserRole(user, result);
    }
  }

  Future<void> _changeUserRole(User user, String newRole) async {
          try {
        await _userService.changeUserRole(user.id, newRole);
        
        // 显示现代化的成功动画
        SuccessAnimationOverlay.show(
          context,
          title: '权限更改成功',
          message: '用户 "${user.username}" 权限已更改为 ${newRole == 'Admin' ? '管理员' : '普通用户'}',
          onComplete: () {
            _loadUsers();
          },
        );
      } catch (e) {
      String errorMessage = '权限更改失败';
      if (e.toString().contains('登录已过期')) {
        errorMessage = '登录已过期，请重新登录';
        // 自动跳转到登录页面
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted) {
            context.go(AppRoutes.login);
          }
        });
      } else if (e.toString().contains('Unauthorized access')) {
        errorMessage = '权限不足：只有管理员可以修改用户权限';
      } else if (e.toString().contains('Network error')) {
        errorMessage = '网络错误：请检查网络连接';
      } else {
        errorMessage = '权限更改失败: ${e.toString().replaceAll('Exception: ', '')}';
      }
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(errorMessage),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
        ),
      );
    }
  }

  Future<void> _toggleUserStatus(User user) async {
    final action = user.isActive ? '封禁' : '解禁';
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('确认$action'),
        content: Text('确定要${action}用户 "${user.username}" 吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: user.isActive ? Colors.red : Colors.green,
            ),
            child: Text(action),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _userService.toggleUserStatus(user.id, !user.isActive);
        
        // 显示现代化的成功动画
        SuccessAnimationOverlay.show(
          context,
          title: '${action}成功',
          message: '用户 "${user.username}" 已${action}',
          onComplete: () {
            _loadUsers();
          },
        );
      } catch (e) {
        String errorMessage = '${action}失败';
        if (e.toString().contains('登录已过期')) {
          errorMessage = '登录已过期，请重新登录';
          // 自动跳转到登录页面
          Future.delayed(const Duration(seconds: 2), () {
            if (mounted) {
              context.go(AppRoutes.login);
            }
          });
        } else if (e.toString().contains('Unauthorized access')) {
          errorMessage = '权限不足：只有管理员可以${action}用户';
        } else if (e.toString().contains('Network error')) {
          errorMessage = '网络错误：请检查网络连接';
        } else {
          errorMessage = '${action}失败: ${e.toString().replaceAll('Exception: ', '')}';
        }
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }

  Future<void> _exportToExcel() async {
    if (_userListResponse == null || _userListResponse!.users.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('没有用户数据可导出'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // 显示保存位置选择对话框
    final saveToDownload = await _showSaveLocationDialog();
    if (saveToDownload == null) return; // 用户取消了选择
    
    try {
      // 显示加载对话框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在导出Excel文件...'),
            ],
          ),
        ),
      );

      // 获取所有用户数据（不分页）
      final allUsersQuery = UserQuery(
        search: _searchController.text.isEmpty ? null : _searchController.text,
        roles: _appliedRoles.isNotEmpty ? _appliedRoles : null,
        departments: _appliedDepartments.isNotEmpty ? _appliedDepartments : null,
        pageNumber: 1,
        pageSize: 1000, // 获取大量数据
        sortBy: _sortBy,
        sortDescending: _sortDescending,
      );
      
      final allUsersResponse = await _userService.getUsers(allUsersQuery);

      // 导出Excel
      final filePath = await ExcelService.exportUsersToExcel(
        allUsersResponse.users,
        saveToDownload: saveToDownload,
      );
      
      // 关闭加载对话框
      if (mounted) {
        Navigator.of(context).pop();

        // 显示现代化的成功动画
        SuccessAnimationOverlay.show(
          context,
          title: '导出成功',
          message: 'Excel文件已保存到: $filePath',
          onComplete: () {
            // 成功动画完成后的回调
          },
        );
      }
    } catch (e) {
      // 关闭加载对话框
      if (mounted) {
        Navigator.of(context).pop();
        
        // 显示错误消息
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('导出失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<bool?> _showSaveLocationDialog() async {
    return showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('选择保存位置'),
          content: const Text('请选择Excel文件的保存位置：'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(null),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('应用目录'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Download文件夹'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: MainLayout(
        currentRoute: '/users',
        child: Column(
          children: [
            // 现代化工具栏
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.06),
                    spreadRadius: 0,
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                  BoxShadow(
                    color: Colors.black.withOpacity(0.02),
                    spreadRadius: 0,
                    blurRadius: 4,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // 现代化工具栏
                  _buildModernToolbar(),
                ],
              ),
            ),
            
            // 条件性显示统计信息（只在有已应用的筛选条件时显示）
            if (_hasActiveFilters)
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.blue[100]!, width: 1),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: Colors.blue[100],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.analytics_rounded,
                        color: Colors.blue[600],
                        size: 16,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        _userListResponse != null ? '共 ${_userListResponse!.totalCount} 条记录' : '共 0 条记录',
                        style: TextStyle(
                          color: Colors.blue[700],
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    if (_isLoading)
                      SizedBox(
                        width: 18,
                        height: 18,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.blue[600]!),
                        ),
                      ),
                    const Spacer(),
                    // 清除筛选按钮
                    GestureDetector(
                      onTap: _clearFilters,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.blue[50],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.clear_rounded,
                              color: Colors.blue[600],
                              size: 14,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '清除',
                              style: TextStyle(
                                color: Colors.blue[600],
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),

            // 用户列表
            Expanded(
              child: _buildUserList(),
            ),
            // 分页
            if (_userListResponse != null) _buildPagination(),
          ],
        ),
      ),
      // 现代化回到顶部按钮
      floatingActionButton: _showScrollToTop
          ? Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                gradient: LinearGradient(
                  colors: [Colors.blue[600]!, Colors.blue[700]!],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.blue.withOpacity(0.3),
                    spreadRadius: 0,
                    blurRadius: 12,
                    offset: const Offset(0, 6),
                  ),
                ],
              ),
              child: FloatingActionButton.small(
                onPressed: _scrollToTop,
                backgroundColor: Colors.transparent,
                elevation: 0,
                child: const Icon(Icons.keyboard_arrow_up_rounded, color: Colors.white, size: 24),
              ),
            )
          : null,
    );
  }

  // 现代化工具栏
  Widget _buildModernToolbar() {
    return Row(
      children: [
        // 动画搜索框
        Expanded(
          child: AnimatedSearchBar(
            controller: _searchController,
            hintText: '搜索用户ID、用户名、邮箱...',
            isSearching: _isSearching,
            onChanged: (value) {
              setState(() {}); // 更新UI
            },
            onClear: () {
              setState(() {}); // 更新UI
            },
          ),
        ),
        const SizedBox(width: 12),
        // 筛选按钮
        Container(
          decoration: BoxDecoration(
            color: (_appliedRoles.isNotEmpty || _appliedDepartments.isNotEmpty)
                ? Colors.blue[50]
                : Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: (_appliedRoles.isNotEmpty || _appliedDepartments.isNotEmpty)
                  ? Colors.blue[200]!
                  : Colors.grey[200]!,
              width: 1
            ),
          ),
          child: Stack(
            children: [
              IconButton(
                onPressed: _showFilterBottomSheet,
                icon: Icon(
                  Icons.tune_rounded,
                  color: (_appliedRoles.isNotEmpty || _appliedDepartments.isNotEmpty)
                      ? Colors.blue[600]
                      : Colors.grey[600],
                  size: 22
                ),
                tooltip: '筛选',
              ),
              if (_appliedRoles.isNotEmpty || _appliedDepartments.isNotEmpty)
                Positioned(
                  right: 8,
                  top: 8,
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: Colors.red[500],
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
            ],
          ),
        ),
        // 只有管理员才能看到导出和添加按钮
        Consumer<AuthProvider>(
          builder: (context, authProvider, child) {
            final currentUser = authProvider.currentUser;
            
            if (currentUser == null || !currentUser.isAdmin) {
              return const SizedBox.shrink();
            }
            
            return Row(
              children: [
                const SizedBox(width: 8),
                // 导出按钮
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.green[600]!, Colors.green[700]!],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.green.withOpacity(0.3),
                        spreadRadius: 0,
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: IconButton(
                    onPressed: _exportToExcel,
                    icon: const Icon(Icons.download_rounded, color: Colors.white, size: 22),
                    tooltip: '导出Excel',
                  ),
                ),
                const SizedBox(width: 8),
                // 添加用户按钮
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.blue[600]!, Colors.blue[700]!],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.blue.withOpacity(0.3),
                        spreadRadius: 0,
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: IconButton(
                    onPressed: () => _navigateToUserForm(),
                    icon: const Icon(Icons.person_add_rounded, color: Colors.white, size: 22),
                    tooltip: '添加用户',
                  ),
                ),
              ],
            );
          },
        ),
      ],
    );
  }





  // 筛选底部弹窗
  void _showFilterBottomSheet() {
    // 创建临时变量来存储弹窗内的选择
    List<String> tempSelectedRoles = List.from(_selectedRoles);
    List<String> tempSelectedDepartments = List.from(_selectedDepartments);
    
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => StatefulBuilder(
        builder: (context, setModalState) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题栏
              Row(
                children: [
                  Icon(Icons.tune_rounded, color: Colors.blue[600], size: 24),
                  const SizedBox(width: 12),
                  const Text(
                    '筛选条件',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close_rounded),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              // 角色筛选
              Text(
                '角色',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
              const SizedBox(height: 12),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  _buildMultiSelectChip('全部角色', null, tempSelectedRoles, (newSelection) {
                    setModalState(() {
                      tempSelectedRoles = newSelection;
                    });
                  }),
                  ..._roles.map((role) =>
                    _buildMultiSelectChip(
                      role == 'Admin' ? '管理员' : '普通用户',
                      role,
                      tempSelectedRoles,
                      (newSelection) {
                        setModalState(() {
                          tempSelectedRoles = newSelection;
                        });
                      }
                    )
                  ),
                ],
              ),
              const SizedBox(height: 20),
              // 部门筛选
              Text(
                '部门',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
              const SizedBox(height: 12),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  _buildMultiSelectChip('全部部门', null, tempSelectedDepartments, (newSelection) {
                    setModalState(() {
                      tempSelectedDepartments = newSelection;
                    });
                  }),
                  ..._departments.map((dept) =>
                    _buildMultiSelectChip(
                      dept,
                      dept,
                      tempSelectedDepartments,
                      (newSelection) {
                        setModalState(() {
                          tempSelectedDepartments = newSelection;
                        });
                      }
                    )
                  ),
                ],
              ),
              const SizedBox(height: 32),
              // 操作按钮
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        setModalState(() {
                          tempSelectedRoles = [];
                          tempSelectedDepartments = [];
                        });
                      },
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text('重置'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        // 应用临时选择到实际状态
                        setState(() {
                          _selectedRoles = tempSelectedRoles;
                          _selectedDepartments = tempSelectedDepartments;
                        });
                        _onFilterChanged();
                        Navigator.pop(context);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue[600],
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text('应用筛选'),
                    ),
                  ),
                ],
              ),
              SizedBox(height: MediaQuery.of(context).viewInsets.bottom),
            ],
          ),
        ),
      ),
    );
  }

  // 新增：构建多选筛选芯片（支持多选）
  Widget _buildMultiSelectChip(String label, String? value, List<String> selectedValues, Function(List<String>) onTap) {
    final isSelected = value == null ? selectedValues.isEmpty : selectedValues.contains(value);

    return GestureDetector(
      onTap: () {
        List<String> newSelection = List.from(selectedValues);
        if (value == null) {
          // "全部类型" 被点击，清空所有选择
          newSelection.clear();
        } else {
          // 具体类型被点击，切换选择状态
          if (newSelection.contains(value)) {
            newSelection.remove(value);
          } else {
            newSelection.add(value);
          }
        }
        onTap(newSelection);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue[600] : Colors.grey[100],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? Colors.blue[600]! : Colors.grey[200]!,
            width: 1,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.grey[700],
            fontSize: 14,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildFilterDropdown<T>({
    required T? value,
    required String hint,
    required IconData icon,
    required List<DropdownMenuItem<T>> items,
    required void Function(T?) onChanged,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<T>(
          value: value,
          isExpanded: true,
          hint: Row(
            children: [
              Icon(icon, color: Colors.grey[500], size: 18),
              const SizedBox(width: 12),
              Text(
                hint,
                style: TextStyle(color: Colors.grey[600], fontSize: 14),
              ),
            ],
          ),
          icon: Icon(Icons.keyboard_arrow_down_rounded, color: Colors.grey[500], size: 20),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          items: items,
          onChanged: onChanged,
          style: const TextStyle(color: Colors.black87, fontSize: 14),
        ),
      ),
    );
  }

  Widget _buildUserList() {
    if (_isLoading && (_userListResponse == null || _userListResponse!.users.isEmpty)) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 64,
                height: 64,
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(32),
                ),
                child: Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.blue[600]!),
                    strokeWidth: 3,
                  ),
                ),
              ),
              const SizedBox(height: 24),
              Text(
                '正在加载用户数据...',
                style: TextStyle(
                  color: Colors.grey[700],
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(40),
                ),
                child: Icon(Icons.error_outline_rounded, size: 40, color: Colors.red[400]),
              ),
              const SizedBox(height: 24),
              Text(
                '加载失败',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _error!,
                style: TextStyle(color: Colors.grey[600], fontSize: 14),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              Consumer<AuthProvider>(
                builder: (context, authProvider, child) {
                  if (!authProvider.isLoggedIn) {
                    return Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Colors.blue[600]!, Colors.blue[700]!],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.blue.withOpacity(0.3),
                            spreadRadius: 0,
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: ElevatedButton.icon(
                        onPressed: () async {
                          await authProvider.initializeAuth();
                          if (authProvider.isLoggedIn) {
                            _loadUsers();
                          } else {
                            if (mounted) {
                              context.go(AppRoutes.login);
                            }
                          }
                        },
                        icon: const Icon(Icons.login_rounded),
                        label: const Text('重新登录'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.transparent,
                          foregroundColor: Colors.white,
                          elevation: 0,
                          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                        ),
                      ),
                    );
                  } else {
                    return Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Colors.blue[600]!, Colors.blue[700]!],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.blue.withOpacity(0.3),
                            spreadRadius: 0,
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: ElevatedButton.icon(
                        onPressed: _loadUsers,
                        icon: const Icon(Icons.refresh_rounded),
                        label: const Text('重新加载'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.transparent,
                          foregroundColor: Colors.white,
                          elevation: 0,
                          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                        ),
                      ),
                    );
                  }
                },
              ),
            ],
          ),
        ),
      );
    }

    if (_userListResponse == null || _userListResponse!.users.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(50),
                ),
                child: Icon(Icons.people_outline_rounded, size: 50, color: Colors.grey[400]),
              ),
              const SizedBox(height: 24),
              Text(
                _hasActiveFilters ? '没有找到匹配的用户' : '暂无用户数据',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[700],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _hasActiveFilters ? '尝试调整搜索或筛选条件' : '点击上方按钮添加第一个用户',
                style: TextStyle(color: Colors.grey[500], fontSize: 14),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: _userListResponse!.users.length,
      itemBuilder: (context, index) {
        final user = _userListResponse!.users[index];
        return _buildModernUserCard(user);
      },
    );
  }

  Widget _buildModernUserCard(User user) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            spreadRadius: 0,
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.02),
            spreadRadius: 0,
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _navigateToUserDetail(user),
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 用户基本信息行
                Row(
                  children: [
                    // 现代化头像
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(14),
                        gradient: user.avatarUrl != null && user.avatarUrl!.isNotEmpty
                            ? null
                            : LinearGradient(
                                colors: user.isActive
                                    ? [_getRoleColor(user.role).withOpacity(0.8), _getRoleColor(user.role)]
                                    : [Colors.grey[400]!, Colors.grey[600]!],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                        boxShadow: [
                          BoxShadow(
                            color: (user.isActive ? _getRoleColor(user.role) : Colors.grey).withOpacity(0.3),
                            spreadRadius: 0,
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: user.avatarUrl != null && user.avatarUrl!.isNotEmpty
                          ? ClipRRect(
                              borderRadius: BorderRadius.circular(14),
                              child: Image.network(
                                _getFullAvatarUrl(user.avatarUrl!),
                                width: 48,
                                height: 48,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  // 如果头像加载失败，显示角色图标
                                  return Container(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(14),
                                      gradient: LinearGradient(
                                        colors: user.isActive
                                            ? [_getRoleColor(user.role).withOpacity(0.8), _getRoleColor(user.role)]
                                            : [Colors.grey[400]!, Colors.grey[600]!],
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                      ),
                                    ),
                                    child: Center(
                                      child: user.role == UserRole.admin
                                          ? const FaIcon(
                                              FontAwesomeIcons.userShield,
                                              color: Colors.white,
                                              size: 20,
                                            )
                                          : const FaIcon(
                                              FontAwesomeIcons.user,
                                              color: Colors.white,
                                              size: 18,
                                            ),
                                    ),
                                  );
                                },
                              ),
                            )
                          : Center(
                              child: user.role == UserRole.admin
                                  ? const FaIcon(
                                      FontAwesomeIcons.userShield,
                                      color: Colors.white,
                                      size: 20,
                                    )
                                  : const FaIcon(
                                      FontAwesomeIcons.user,
                                      color: Colors.white,
                                      size: 18,
                                    ),
                            ),
                    ),
                    const SizedBox(width: 16),
                    // 用户信息
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  user.fullName ?? user.username,
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w700,
                                    color: user.isActive ? Colors.black87 : Colors.grey[600],
                                    letterSpacing: -0.2,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              // 状态标签
                              if (!user.isActive)
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                  decoration: BoxDecoration(
                                    color: Colors.red[50],
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(color: Colors.red[200]!, width: 1),
                                  ),
                                  child: Text(
                                    '已封禁',
                                    style: TextStyle(
                                      color: Colors.red[600],
                                      fontSize: 11,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          Text(
                            user.email,
                            style: TextStyle(
                              color: user.isActive ? Colors.grey[600] : Colors.grey[500],
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 2),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.purple[50],
                              borderRadius: BorderRadius.circular(6),
                              border: Border.all(color: Colors.purple[200]!, width: 1),
                            ),
                            child: Text(
                              'ID: ${user.displayId}',
                              style: TextStyle(
                                color: Colors.purple[700],
                                fontSize: 11,
                                fontWeight: FontWeight.w600,
                                letterSpacing: 0.5,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    // 操作菜单
                    Consumer<AuthProvider>(
                      builder: (context, authProvider, child) {
                        final currentUser = authProvider.currentUser;
                        
                        // 只有管理员才能看到操作菜单
                        if (currentUser == null || !currentUser.isAdmin) {
                          return const SizedBox(width: 24);
                        }
                        
                        final isCurrentUser = currentUser.id == user.id;
                        final isTargetAdmin = user.role == UserRole.admin;
                        
                        // 不能对自己或其他管理员进行某些操作
                        final canChangeRole = !isCurrentUser && !isTargetAdmin;
                        final canToggleStatus = !isCurrentUser && !isTargetAdmin;
                        final canDelete = !isCurrentUser && !isTargetAdmin;
                        final hasAnyAction = canChangeRole || canToggleStatus || canDelete;
                        
                        if (!hasAnyAction) {
                          return const SizedBox(width: 24);
                        }
                        
                        return Container(
                          decoration: BoxDecoration(
                            color: Colors.grey[50],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: PopupMenuButton<String>(
                            icon: Icon(Icons.more_horiz_rounded, color: Colors.grey[600], size: 20),
                            padding: EdgeInsets.zero,
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                            onSelected: (value) {
                              switch (value) {
                                case 'change_role':
                                  _showChangeRoleDialog(user);
                                  break;
                                case 'toggle_status':
                                  _toggleUserStatus(user);
                                  break;
                                case 'delete':
                                  _deleteUser(user);
                                  break;
                              }
                            },
                            itemBuilder: (context) => [
                              if (canChangeRole)
                                const PopupMenuItem(
                                  value: 'change_role',
                                  child: Row(
                                    children: [
                                      Icon(Icons.admin_panel_settings_rounded, size: 18),
                                      SizedBox(width: 12),
                                      Text('更改权限'),
                                    ],
                                  ),
                                ),
                              if (canToggleStatus)
                                PopupMenuItem(
                                  value: 'toggle_status',
                                  child: Row(
                                    children: [
                                      Icon(
                                        user.isActive ? Icons.block_rounded : Icons.check_circle_rounded,
                                        size: 18,
                                        color: user.isActive ? Colors.red : Colors.green,
                                      ),
                                      const SizedBox(width: 12),
                                      Text(
                                        user.isActive ? '封禁用户' : '解禁用户',
                                        style: TextStyle(
                                          color: user.isActive ? Colors.red : Colors.green,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              if (canDelete)
                                const PopupMenuItem(
                                  value: 'delete',
                                  child: Row(
                                    children: [
                                      Icon(Icons.delete_rounded, size: 18, color: Colors.red),
                                      SizedBox(width: 12),
                                      Text('删除', style: TextStyle(color: Colors.red)),
                                    ],
                                  ),
                                ),
                            ],
                          ),
                        );
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                // 信息标签行
                Row(
                  children: [
                    _buildModernChip('角色', user.role == UserRole.admin ? '管理员' : '普通用户', _getRoleColor(user.role)),
                    const SizedBox(width: 8),
                    if (user.department != null && user.department!.isNotEmpty) ...[
                      _buildModernChip('部门', user.department!, Colors.indigo),
                      const SizedBox(width: 8),
                    ],
                    const Spacer(),
                    _buildModernChip('资产', user.assignedAssetsCount.toString(), Colors.amber[700]!),
                  ],
                ),
                const SizedBox(height: 12),
                // 底部信息
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(color: Colors.grey[200]!, width: 1),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.schedule_rounded, color: Colors.grey[600], size: 16),
                      const SizedBox(width: 8),
                      Text(
                        '创建于 ${TimezoneUtils.formatDateTime(user.createdAt)}',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildModernChip(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: color.withOpacity(0.3), width: 1),
      ),
      child: Text(
        '$label: $value',
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }



  // 获取完整的头像URL
  String _getFullAvatarUrl(String avatarUrl) {
    if (avatarUrl.startsWith('http://') || avatarUrl.startsWith('https://')) {
      return avatarUrl;
    } else {
      // 相对URL，需要添加基础URL
      return 'http://********:5000$avatarUrl';
    }
  }

  Color _getRoleColor(String role) {
    switch (role.toLowerCase()) {
      case 'admin':
        return Colors.red;
      case 'normal user':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  Widget _buildPagination() {
    final response = _userListResponse!;
    final totalPages = response.totalPages;
    
    if (totalPages <= 1) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            spreadRadius: 0,
            blurRadius: 12,
            offset: const Offset(0, -4),
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.02),
            spreadRadius: 0,
            blurRadius: 4,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: Colors.grey[200]!, width: 1),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline_rounded, color: Colors.grey[600], size: 16),
                const SizedBox(width: 8),
                Text(
                  '共 ${response.totalCount} 条记录',
                  style: TextStyle(
                    color: Colors.grey[700],
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          Row(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: _currentPage > 1 ? Colors.blue[50] : Colors.grey[100],
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                    color: _currentPage > 1 ? Colors.blue[200]! : Colors.grey[200]!,
                    width: 1,
                  ),
                ),
                child: IconButton(
                  onPressed: _currentPage > 1 ? () => _onPageChanged(_currentPage - 1) : null,
                  icon: Icon(
                    Icons.chevron_left_rounded,
                    color: _currentPage > 1 ? Colors.blue[600] : Colors.grey[400],
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.blue[600]!, Colors.blue[700]!],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(10),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.blue.withOpacity(0.3),
                      spreadRadius: 0,
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Text(
                  '$_currentPage / $totalPages',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Container(
                decoration: BoxDecoration(
                  color: _currentPage < totalPages ? Colors.blue[50] : Colors.grey[100],
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                    color: _currentPage < totalPages ? Colors.blue[200]! : Colors.grey[200]!,
                    width: 1,
                  ),
                ),
                child: IconButton(
                  onPressed: _currentPage < totalPages ? () => _onPageChanged(_currentPage + 1) : null,
                  icon: Icon(
                    Icons.chevron_right_rounded,
                    color: _currentPage < totalPages ? Colors.blue[600] : Colors.grey[400],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
} 