using Microsoft.EntityFrameworkCore;
using ITAssetAPI.Data;
using ITAssetAPI.Models;
using ITAssetAPI.DTOs;
using BCrypt.Net;

namespace ITAssetAPI.Services
{
    public class UserService : IUserService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<UserService> _logger;

        public UserService(ApplicationDbContext context, ILogger<UserService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<UserListDto> GetUsersAsync(UserQueryDto query)
        {
            try
            {
                var usersQuery = _context.Users
                    .Include(u => u.AssignedAssets)
                    .AsQueryable();

                // Apply filters - 模糊搜索用户ID、用户名和邮箱
                if (!string.IsNullOrEmpty(query.Search))
                {
                    var searchTerm = query.Search.ToLower(); // 转换为小写进行不区分大小写的搜索
                    usersQuery = usersQuery.Where(u => 
                        u.Username.ToLower().Contains(searchTerm) ||
                        u.Email.ToLower().Contains(searchTerm) ||
                        ("USR" + u.Id.ToString().PadLeft(5, '0')).ToLower().Contains(searchTerm)); // 支持用户ID搜索 (USR00001格式)
                }

                // 角色筛选（支持多个角色，逗号分隔）
                if (!string.IsNullOrEmpty(query.Role))
                {
                    var roles = query.Role.Split(',', StringSplitOptions.RemoveEmptyEntries)
                        .Select(r => r.Trim())
                        .ToList();
                    
                    if (roles.Any())
                    {
                        usersQuery = usersQuery.Where(u => roles.Contains(u.Role));
                    }
                }

                // 部门筛选（支持多个部门，逗号分隔）
                if (!string.IsNullOrEmpty(query.Department))
                {
                    var departments = query.Department.Split(',', StringSplitOptions.RemoveEmptyEntries)
                        .Select(d => d.Trim())
                        .ToList();
                    
                    if (departments.Any())
                    {
                        usersQuery = usersQuery.Where(u => u.Department != null && departments.Contains(u.Department));
                    }
                }

                // Apply sorting
                usersQuery = query.SortBy?.ToLower() switch
                {
                    "username" => query.SortDescending ? usersQuery.OrderByDescending(u => u.Username) : usersQuery.OrderBy(u => u.Username),
                    "email" => query.SortDescending ? usersQuery.OrderByDescending(u => u.Email) : usersQuery.OrderBy(u => u.Email),
                    "role" => query.SortDescending ? usersQuery.OrderByDescending(u => u.Role) : usersQuery.OrderBy(u => u.Role),
                    "fullname" => query.SortDescending ? usersQuery.OrderByDescending(u => u.FullName) : usersQuery.OrderBy(u => u.FullName),
                    "department" => query.SortDescending ? usersQuery.OrderByDescending(u => u.Department) : usersQuery.OrderBy(u => u.Department),
                    _ => query.SortDescending ? usersQuery.OrderByDescending(u => u.CreatedAt) : usersQuery.OrderBy(u => u.CreatedAt)
                };

                var totalCount = await usersQuery.CountAsync();
                var totalPages = (int)Math.Ceiling((double)totalCount / query.PageSize);

                var users = await usersQuery
                    .Skip((query.PageNumber - 1) * query.PageSize)
                    .Take(query.PageSize)
                    .Select(u => new UserDto
                    {
                        Id = u.Id,
                        Username = u.Username,
                        Email = u.Email,
                        Role = u.Role,
                        FullName = u.FullName,
                        Department = u.Department,
                        AvatarUrl = u.AvatarUrl,
                        IsActive = u.IsActive,
                        CreatedAt = u.CreatedAt,
                        UpdatedAt = u.UpdatedAt,
                        AssignedAssetsCount = u.AssignedAssets.Count
                    })
                    .ToListAsync();

                return new UserListDto
                {
                    Users = users,
                    TotalCount = totalCount,
                    PageNumber = query.PageNumber,
                    PageSize = query.PageSize,
                    TotalPages = totalPages
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting users");
                throw;
            }
        }

        public async Task<UserDto?> GetUserByIdAsync(int id)
        {
            try
            {
                var user = await _context.Users
                    .Include(u => u.AssignedAssets)
                    .FirstOrDefaultAsync(u => u.Id == id);

                if (user == null) return null;

                return new UserDto
                {
                    Id = user.Id,
                    Username = user.Username,
                    Email = user.Email,
                    Role = user.Role,
                    FullName = user.FullName,
                    Department = user.Department,
                    AvatarUrl = user.AvatarUrl,
                    IsActive = user.IsActive,
                    CreatedAt = user.CreatedAt,
                    UpdatedAt = user.UpdatedAt,
                    AssignedAssetsCount = user.AssignedAssets.Count
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user by id {UserId}", id);
                throw;
            }
        }

        public async Task<UserDto?> GetUserByUsernameAsync(string username)
        {
            try
            {
                var user = await _context.Users
                    .Include(u => u.AssignedAssets)
                    .FirstOrDefaultAsync(u => u.Username == username);

                if (user == null) return null;

                return new UserDto
                {
                    Id = user.Id,
                    Username = user.Username,
                    Email = user.Email,
                    Role = user.Role,
                    FullName = user.FullName,
                    Department = user.Department,
                    AvatarUrl = user.AvatarUrl,
                    IsActive = user.IsActive,
                    CreatedAt = user.CreatedAt,
                    UpdatedAt = user.UpdatedAt,
                    AssignedAssetsCount = user.AssignedAssets.Count
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user by username {Username}", username);
                throw;
            }
        }

        public async Task<UserDto> CreateUserAsync(CreateUserDto createUserDto)
        {
            try
            {
                // Check if username already exists
                if (await UsernameExistsAsync(createUserDto.Username))
                {
                    throw new InvalidOperationException("Username already exists");
                }

                // Check if email already exists
                if (await EmailExistsAsync(createUserDto.Email))
                {
                    throw new InvalidOperationException("Email already exists");
                }

                var user = new User
                {
                    Username = createUserDto.Username,
                    Email = createUserDto.Email,
                    PasswordHash = HashPassword(createUserDto.Password),
                    Role = createUserDto.Role,
                    FullName = createUserDto.FullName,
                    Department = createUserDto.Department,
                    AvatarUrl = createUserDto.AvatarUrl,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.Users.Add(user);
                await _context.SaveChangesAsync();

                return new UserDto
                {
                    Id = user.Id,
                    Username = user.Username,
                    Email = user.Email,
                    Role = user.Role,
                    FullName = user.FullName,
                    Department = user.Department,
                    AvatarUrl = user.AvatarUrl,
                    IsActive = user.IsActive,
                    CreatedAt = user.CreatedAt,
                    UpdatedAt = user.UpdatedAt,
                    AssignedAssetsCount = 0
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating user");
                throw;
            }
        }

        public async Task<UserDto?> UpdateUserAsync(int id, UpdateUserDto updateUserDto)
        {
            try
            {
                var user = await _context.Users.FindAsync(id);
                if (user == null) return null;

                // Check if username is being changed and if it already exists
                if (!string.IsNullOrEmpty(updateUserDto.Username) && 
                    updateUserDto.Username != user.Username &&
                    await UsernameExistsAsync(updateUserDto.Username, id))
                {
                    throw new InvalidOperationException("Username already exists");
                }

                // Check if email is being changed and if it already exists
                if (!string.IsNullOrEmpty(updateUserDto.Email) && 
                    updateUserDto.Email != user.Email &&
                    await EmailExistsAsync(updateUserDto.Email, id))
                {
                    throw new InvalidOperationException("Email already exists");
                }

                // Update fields
                if (!string.IsNullOrEmpty(updateUserDto.Username))
                    user.Username = updateUserDto.Username;
                
                if (!string.IsNullOrEmpty(updateUserDto.Email))
                    user.Email = updateUserDto.Email;
                
                if (!string.IsNullOrEmpty(updateUserDto.Role))
                    user.Role = updateUserDto.Role;
                
                if (updateUserDto.FullName != null)
                    user.FullName = updateUserDto.FullName;
                
                if (updateUserDto.Department != null)
                    user.Department = updateUserDto.Department;
                
                if (updateUserDto.AvatarUrl != null)
                    user.AvatarUrl = updateUserDto.AvatarUrl;

                user.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return await GetUserByIdAsync(id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user {UserId}", id);
                throw;
            }
        }

        public async Task<bool> DeleteUserAsync(int id)
        {
            try
            {
                var user = await _context.Users.FindAsync(id);
                if (user == null) return false;

                // Check if user has assigned assets
                var hasAssignedAssets = await _context.Assets.AnyAsync(a => a.AssignedUserId == id);
                if (hasAssignedAssets)
                {
                    throw new InvalidOperationException("Cannot delete user with assigned assets");
                }

                _context.Users.Remove(user);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting user {UserId}", id);
                throw;
            }
        }

        public async Task<bool> ChangePasswordAsync(int id, ChangePasswordDto changePasswordDto)
        {
            try
            {
                var user = await _context.Users.FindAsync(id);
                if (user == null) return false;

                // Verify current password
                if (!VerifyPassword(changePasswordDto.CurrentPassword, user.PasswordHash))
                {
                    throw new InvalidOperationException("Current password is incorrect");
                }

                user.PasswordHash = HashPassword(changePasswordDto.NewPassword);
                user.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error changing password for user {UserId}", id);
                throw;
            }
        }

        public async Task<bool> UserExistsAsync(int id)
        {
            return await _context.Users.AnyAsync(u => u.Id == id);
        }

        public async Task<bool> UsernameExistsAsync(string username, int? excludeId = null)
        {
            var query = _context.Users.Where(u => u.Username == username);
            if (excludeId.HasValue)
            {
                query = query.Where(u => u.Id != excludeId.Value);
            }
            return await query.AnyAsync();
        }

        public async Task<bool> EmailExistsAsync(string email, int? excludeId = null)
        {
            var query = _context.Users.Where(u => u.Email == email);
            if (excludeId.HasValue)
            {
                query = query.Where(u => u.Id != excludeId.Value);
            }
            return await query.AnyAsync();
        }

        public async Task<List<string>> GetRolesAsync()
        {
            return await _context.Users
                .Select(u => u.Role)
                .Distinct()
                .OrderBy(r => r)
                .ToListAsync();
        }

        public async Task<List<string>> GetDepartmentsAsync()
        {
            // 预设的部门列表
            var predefinedDepartments = new List<string>
            {
                "IT部门",
                "人力资源部",
                "财务部",
                "法务部门",
                "市场营销部",
                "销售部",
                "运营部",
                "产品部",
                "研发部",
                "客服部",
                "行政部",
                "采购部",
                "质量管理部",
                "安全部",
                "培训部"
            };

            // 获取数据库中现有的部门
            var existingDepartments = await _context.Users
                .Where(u => u.Department != null)
                .Select(u => u.Department!)
                .Distinct()
                .ToListAsync();

            // 合并预设部门和现有部门，去重并排序
            var allDepartments = predefinedDepartments
                .Union(existingDepartments)
                .Distinct()
                .OrderBy(d => d)
                .ToList();

            return allDepartments;
        }

        public async Task<bool> AssignRoleAsync(int userId, string role)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null) return false;

                user.Role = role;
                user.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error assigning role to user {UserId}", userId);
                throw;
            }
        }

        public async Task<bool> ChangeUserRoleAsync(int userId, string role, int currentUserId)
        {
            try
            {
                var targetUser = await _context.Users.FindAsync(userId);
                if (targetUser == null) return false;

                var currentUser = await _context.Users.FindAsync(currentUserId);
                if (currentUser == null || currentUser.Role != "Admin")
                {
                    throw new InvalidOperationException("Only admins can change user roles");
                }

                // Prevent admin from demoting other admins to normal user
                if (targetUser.Role == "Admin" && role == "Normal User" && targetUser.Id != currentUserId)
                {
                    throw new InvalidOperationException("Admins cannot demote other admins to normal user");
                }

                targetUser.Role = role;
                targetUser.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error changing user role for user {UserId}", userId);
                throw;
            }
        }

        public async Task<bool> ToggleUserStatusAsync(int userId, bool isActive)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null) return false;

                user.IsActive = isActive;
                user.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error toggling user status for user {UserId}", userId);
                throw;
            }
        }

        public async Task<UserDto?> UpdateAvatarAsync(int userId, string? avatarUrl)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null) return null;

                user.AvatarUrl = avatarUrl;
                user.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                return await GetUserByIdAsync(userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating avatar for user {UserId}", userId);
                throw;
            }
        }

        public async Task<string> UploadAvatarAsync(int userId, IFormFile file)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    throw new InvalidOperationException("User not found");
                }

                // Create uploads directory if it doesn't exist
                var uploadsPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "uploads", "avatars");
                if (!Directory.Exists(uploadsPath))
                {
                    Directory.CreateDirectory(uploadsPath);
                }

                // Generate unique filename
                var fileExtension = Path.GetExtension(file.FileName);
                var fileName = $"{userId}_{Guid.NewGuid()}{fileExtension}";
                var filePath = Path.Combine(uploadsPath, fileName);

                // Save file
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                // Generate URL
                var avatarUrl = $"/uploads/avatars/{fileName}";

                // Update user avatar URL
                _logger.LogInformation("Updating user {UserId} avatar URL to: {AvatarUrl}", userId, avatarUrl);
                user.AvatarUrl = avatarUrl;
                user.UpdatedAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();
                _logger.LogInformation("User {UserId} avatar URL updated successfully", userId);

                return avatarUrl;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading avatar for user {UserId}", userId);
                throw;
            }
        }

        private string HashPassword(string password)
        {
            return BCrypt.Net.BCrypt.HashPassword(password);
        }

        private bool VerifyPassword(string password, string hash)
        {
            return BCrypt.Net.BCrypt.Verify(password, hash);
        }
    }
} 