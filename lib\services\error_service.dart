import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class ErrorService {
  static String getLocalizedErrorMessage(String serverMessage, BuildContext context) {
    final l10n = AppLocalizations.of(context);
    
    // 如果无法获取本地化，直接返回原消息
    if (l10n == null) return serverMessage;
    
    // 映射常见的服务器错误消息
    final errorMappings = {
      '用户不存在，请检查用户名': l10n.userNotExists,
      '密码错误，请重新输入': l10n.passwordIncorrect,
      '账户已被封禁，请联系管理员': l10n.accountDisabled,
      '用户名或密码错误': l10n.loginFailed,
      'Username or password is incorrect': l10n.loginFailed,
      '登录失败: 用户不存在，请检查用户名': l10n.userNotExists,
      '登录失败: 密码错误，请重新输入': l10n.passwordIncorrect,
      '登录失败: 账户已被封禁，请联系管理员': l10n.accountDisabled,
      '登录失败: 用户名或密码错误': l10n.loginFailed,
      '验证错误': l10n.validationError,
      '请求错误': l10n.requestError,
      '权限不足，请联系管理员': l10n.permissionDenied,
      '请求的资源不存在': l10n.resourceNotFound,
      '数据冲突，请刷新后重试': l10n.dataConflict,
      '数据验证失败': l10n.dataValidationFailed,
      '服务器内部错误，请稍后重试': l10n.serverError,
      '服务暂时不可用，请稍后重试': l10n.serviceUnavailable,
      '请求失败，请重试': l10n.requestFailed,
      '网络连接超时，请检查网络连接': l10n.networkTimeout,
      '网络连接失败，请检查网络设置': l10n.networkError,
      '请求已取消': l10n.requestCancelled,
      '未知错误': l10n.unknownError,
    };
    
    // 首先尝试完全匹配
    String? mappedMessage = errorMappings[serverMessage];
    
    // 如果没有完全匹配，尝试部分匹配
    if (mappedMessage == null) {
      for (String key in errorMappings.keys) {
        if (serverMessage.contains(key)) {
          mappedMessage = errorMappings[key];
          break;
        }
      }
    }
    
    // 如果仍然没有匹配，检查是否包含特定关键词
    if (mappedMessage == null) {
      if (serverMessage.contains('用户不存在') || 
          serverMessage.contains('User does not exist') ||
          serverMessage.contains('user not found')) {
        mappedMessage = l10n.userNotExists;
      } else if (serverMessage.contains('密码错误') || 
                 serverMessage.contains('密码不正确') ||
                 serverMessage.contains('Password is incorrect') ||
                 serverMessage.contains('Invalid password') ||
                 serverMessage.contains('password')) {
        mappedMessage = l10n.passwordIncorrect;
      } else if (serverMessage.contains('账户') && serverMessage.contains('封禁') ||
                 serverMessage.contains('Account') && serverMessage.contains('disabled')) {
        mappedMessage = l10n.accountDisabled;
      } else if (serverMessage.contains('用户名或密码错误') ||
                 serverMessage.contains('Username or password') ||
                 serverMessage.contains('Invalid credentials') ||
                 serverMessage.contains('Authentication failed') ||
                 serverMessage.contains('登录失败') || 
                 serverMessage.contains('Login failed')) {
        mappedMessage = l10n.loginFailed;
      } else if (serverMessage.contains('网络') && serverMessage.contains('超时') ||
                 serverMessage.contains('Network timeout') ||
                 serverMessage.contains('Connection timeout')) {
        mappedMessage = l10n.networkTimeout;
      } else if (serverMessage.contains('网络') ||
                 serverMessage.contains('Network') ||
                 serverMessage.contains('Connection failed')) {
        mappedMessage = l10n.networkError;
      } else if (serverMessage.contains('服务器') ||
                 serverMessage.contains('Server error') ||
                 serverMessage.contains('Internal server error')) {
        mappedMessage = l10n.serverError;
      }
    }
    
    return mappedMessage ?? serverMessage;
  }
} 