<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  
  <!-- Authentication Messages -->
  <data name="InvalidCredentials" xml:space="preserve">
    <value>用户名或密码错误</value>
  </data>
  <data name="UserNotFound" xml:space="preserve">
    <value>用户不存在</value>
  </data>
  <data name="LoginSuccessful" xml:space="preserve">
    <value>登录成功</value>
  </data>
  <data name="InvalidToken" xml:space="preserve">
    <value>无效的用户令牌</value>
  </data>
  <data name="TokenExpired" xml:space="preserve">
    <value>令牌已过期</value>
  </data>
  
  <!-- Ticket Messages -->
  <data name="TicketNotFound" xml:space="preserve">
    <value>工单不存在</value>
  </data>
  <data name="TicketCreated" xml:space="preserve">
    <value>工单创建成功</value>
  </data>
  <data name="TicketUpdated" xml:space="preserve">
    <value>工单更新成功</value>
  </data>
  <data name="TicketAssigned" xml:space="preserve">
    <value>工单分配成功</value>
  </data>
  <data name="TicketStatusUpdated" xml:space="preserve">
    <value>工单状态更新成功</value>
  </data>
  <data name="InvalidTicketStatus" xml:space="preserve">
    <value>无效的工单状态</value>
  </data>
  <data name="CannotChangeClosedTicketStatus" xml:space="preserve">
    <value>已关闭的工单不能再切换状态</value>
  </data>
  <data name="RollbackChanceAlreadyUsed" xml:space="preserve">
    <value>已使用过一次回溯机会</value>
  </data>
  <data name="CannotCloseFromCurrentStatus" xml:space="preserve">
    <value>当前状态不允许关闭</value>
  </data>
  
  <!-- Comment Messages -->
  <data name="CommentAdded" xml:space="preserve">
    <value>评论添加成功</value>
  </data>
  <data name="CommentDeleted" xml:space="preserve">
    <value>评论删除成功</value>
  </data>
  <data name="CommentNotFound" xml:space="preserve">
    <value>评论不存在</value>
  </data>
  <data name="CannotDeleteOthersComment" xml:space="preserve">
    <value>不能删除其他用户的评论</value>
  </data>
  <data name="CommentsDeleted" xml:space="preserve">
    <value>成功删除了 {0} 条评论</value>
  </data>
  
  <!-- Validation Messages -->
  <data name="RequiredField" xml:space="preserve">
    <value>此字段为必填项</value>
  </data>
  <data name="InvalidEmailFormat" xml:space="preserve">
    <value>邮箱格式无效</value>
  </data>
  <data name="PasswordTooShort" xml:space="preserve">
    <value>密码长度至少为6位</value>
  </data>
  
  <!-- General Messages -->
  <data name="OperationSuccessful" xml:space="preserve">
    <value>操作成功</value>
  </data>
  <data name="OperationFailed" xml:space="preserve">
    <value>操作失败</value>
  </data>
  <data name="UnauthorizedAccess" xml:space="preserve">
    <value>未授权访问</value>
  </data>
  <data name="InternalServerError" xml:space="preserve">
    <value>服务器内部错误</value>
  </data>
  <data name="BadRequest" xml:space="preserve">
    <value>请求无效</value>
  </data>
</root>
