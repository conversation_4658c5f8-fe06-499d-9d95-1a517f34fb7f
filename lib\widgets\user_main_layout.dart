import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../providers/auth_provider.dart';
import '../config/routes.dart';

class UserMainLayout extends StatefulWidget {
  final Widget child;
  final String currentRoute;
  final String? title;
  final bool showBackButton;
  final Widget? floatingActionButton;
  final VoidCallback? onBackPressed;
  final List<Widget>? actions;

  const UserMainLayout({
    super.key,
    required this.child,
    required this.currentRoute,
    this.title,
    this.showBackButton = false,
    this.floatingActionButton,
    this.onBackPressed,
    this.actions,
  });

  @override
  State<UserMainLayout> createState() => _UserMainLayoutState();
}

class _UserMainLayoutState extends State<UserMainLayout> {
  int _getCurrentIndex() {
    switch (widget.currentRoute) {
      case AppRoutes.userDashboard:
        return 0;
      case AppRoutes.userAssets:
        return 1;
      case AppRoutes.ticketList:
        return 2;
      case AppRoutes.userProfile:
        return 3;
      default:
        // 对于子页面，根据父页面确定索引
        if (widget.currentRoute.startsWith('/user-assets')) {
          return 1; // 资产相关页面
        } else if (widget.currentRoute.startsWith('/tickets')) {
          return 2; // 工单相关页面
        }
        return 0; // 默认仪表板
    }
  }

  void _onTabTapped(int index) {
    switch (index) {
      case 0:
        context.go(AppRoutes.userDashboard);
        break;
      case 1:
        context.go(AppRoutes.userAssets);
        break;
      case 2:
        context.go(AppRoutes.ticketList);
        break;
      case 3:
        context.go(AppRoutes.userProfile);
        break;
    }
  }

  String _getTitle(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    if (widget.title != null) {
      return widget.title!;
    }

    switch (widget.currentRoute) {
      case AppRoutes.userDashboard:
        return l10n.myWorkspace;
      case AppRoutes.userAssets:
        return l10n.myAssets;
      case AppRoutes.ticketList:
        return l10n.ticketManagement;
      case AppRoutes.userProfile:
        return l10n.userProfile;
      default:
        if (widget.currentRoute.startsWith('/user-assets')) {
          return l10n.myAssets;
        } else if (widget.currentRoute.startsWith('/tickets')) {
          if (widget.currentRoute.contains('/new')) {
            return l10n.createTicket;
          } else if (widget.currentRoute.contains('/tickets/') && !widget.currentRoute.contains('/new')) {
            return l10n.ticketDetails;
          }
          return l10n.ticketManagement;
        }
        return l10n.myWorkspace;
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(_getTitle(context)),
        leading: widget.showBackButton
            ? IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: widget.onBackPressed ?? () {
                  if (Navigator.of(context).canPop()) {
                    Navigator.of(context).pop();
                  } else {
                    // 如果无法返回，根据当前页面跳转到合适的父页面
                    if (widget.currentRoute.startsWith('/user-assets')) {
                      context.go(AppRoutes.userAssets);
                    } else if (widget.currentRoute.startsWith('/tickets')) {
                      context.go(AppRoutes.ticketList);
                    } else {
                      context.go(AppRoutes.userDashboard);
                    }
                  }
                },
              )
            : null,
        actions: [
          // 自定义actions
          if (widget.actions != null) ...widget.actions!,
          // 默认的退出登录按钮
          IconButton(
            onPressed: () async {
              final confirmed = await showDialog<bool>(
                context: context,
                builder: (dialogContext) => AlertDialog(
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                  title: Row(
                    children: [
                      Icon(Icons.logout, color: Colors.orange[600]),
                      const SizedBox(width: 8),
                      Text(l10n.confirmLogout),
                    ],
                  ),
                  content: Text(l10n.confirmLogoutMessage),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(dialogContext).pop(false),
                      child: Text(
                        l10n.cancel,
                        style: TextStyle(color: Colors.grey[600]),
                      ),
                    ),
                    ElevatedButton(
                      onPressed: () => Navigator.of(dialogContext).pop(true),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange[600],
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(l10n.logout),
                    ),
                  ],
                ),
              );

              if (confirmed == true) {
                final authProvider = Provider.of<AuthProvider>(context, listen: false);
                await authProvider.logout();
                if (context.mounted) {
                  context.go(AppRoutes.login);
                }
              }
            },
            icon: const Icon(Icons.logout),
            tooltip: l10n.logoutTooltip,
          ),
        ],
      ),
      body: widget.child,
      floatingActionButton: widget.floatingActionButton,
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _getCurrentIndex(),
        onTap: _onTabTapped,
        type: BottomNavigationBarType.fixed,
        selectedItemColor: Theme.of(context).primaryColor,
        unselectedItemColor: Colors.grey,
        items: [
          BottomNavigationBarItem(
            icon: const Icon(Icons.dashboard),
            label: l10n.workspace,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.inventory_2),
            label: l10n.myAssets,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.support_agent),
            label: l10n.ticketManagement,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.person),
            label: l10n.userProfile,
          ),
        ],
      ),
    );
  }
}
