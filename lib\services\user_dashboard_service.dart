import '../models/user_dashboard_stats.dart';
import '../models/asset.dart';
import 'api_service.dart';

class UserDashboardService {
  final ApiService _apiService = ApiService();

  Future<UserDashboardStats> getUserDashboardStats() async {
    try {
      await _apiService.initializeToken();

      // 尝试获取真实数据
      try {
        // 获取当前用户信息
        final currentUserResponse = await _apiService.dio.get('/auth/me');
        final currentUserId = currentUserResponse.data['id'];

        // 确保用户ID是整数
        final userIdInt = currentUserId is int ? currentUserId : int.tryParse(currentUserId.toString());
        if (userIdInt == null) {
          throw Exception('无效的用户ID: $currentUserId');
        }

        // 使用现有的资产API获取所有资产，然后筛选用户的资产
        final allAssetsResponse = await _apiService.dio.get('/assets?page=1&limit=1000'); // 获取大量资产
        final List<dynamic> allAssetsData = allAssetsResponse.data['assets'] ?? [];

        // 筛选分配给当前用户的资产
        final userAssets = allAssetsData.where((assetJson) {
          final assignedUserId = assetJson['assignedUserId'];
          return assignedUserId != null && assignedUserId.toString() == userIdInt.toString();
        }).toList();

        // 计算用户资产统计
        final assignedToMeCount = userAssets.length;
        final activeAssetsCount = userAssets.where((asset) => asset['status'] == 0 || asset['status'] == 'Available').length; // Available状态（未分配）
        final maintenanceAssetsCount = userAssets.where((asset) => asset['status'] == 2 || asset['status'] == 'Maintenance').length; // 只显示分配给该用户且正在维护的资产

        // 获取最近的3个资产
        final recentAssetsData = userAssets.take(3).toList();

        final recentAssets = <UserAssetSummary>[];
        for (final assetJson in recentAssetsData) {
          try {
            final asset = Asset.fromJson(assetJson);
            final summary = UserAssetSummary.fromAsset(asset);
            recentAssets.add(summary);
          } catch (e) {
            // 解析失败时跳过该资产
            continue;
          }
        }

        final result = UserDashboardStats(
          assignedToMeCount: assignedToMeCount,
          activeAssetsCount: activeAssetsCount,
          maintenanceAssetsCount: maintenanceAssetsCount,
          pendingTicketsCount: 0, // TODO: 实现工单统计
          completedTicketsCount: 0, // TODO: 实现工单统计
          recentAssets: recentAssets,
          recentTickets: [], // TODO: 实现最近工单
        );

        return result;
      } catch (apiError) {
        // 如果API调用失败，回退到模拟数据
        return _getMockUserDashboardStats();
      }
    } catch (e) {
      throw Exception('获取用户仪表盘数据失败: $e');
    }
  }

  // 模拟数据，用于开发测试
  UserDashboardStats _getMockUserDashboardStats() {
    return UserDashboardStats(
      assignedToMeCount: 5,
      activeAssetsCount: 4,
      maintenanceAssetsCount: 1,
      pendingTicketsCount: 2,
      completedTicketsCount: 8,
      recentAssets: [
        UserAssetSummary(
          id: 1,
          name: 'MacBook Pro 16"',
          assetNumber: 'LAP-001',
          category: 'Laptop',
          status: 'Assigned',
          assignedDate: DateTime.now().subtract(const Duration(days: 30)),
        ),
        UserAssetSummary(
          id: 2,
          name: 'iPhone 14 Pro',
          assetNumber: 'MOB-001',
          category: 'Mobile',
          status: 'Assigned',
          assignedDate: DateTime.now().subtract(const Duration(days: 15)),
        ),
        UserAssetSummary(
          id: 3,
          name: 'Dell Monitor 27"',
          assetNumber: 'MON-001',
          category: 'Monitor',
          status: 'Assigned',
          assignedDate: DateTime.now().subtract(const Duration(days: 45)),
        ),
      ],
      recentTickets: [
        UserTicketSummary(
          id: 1,
          title: '电脑运行缓慢',
          status: 'In Progress',
          priority: 'Medium',
          createdDate: DateTime.now().subtract(const Duration(days: 2)),
          updatedDate: DateTime.now().subtract(const Duration(hours: 6)),
        ),
        UserTicketSummary(
          id: 2,
          title: '软件安装请求',
          status: 'Pending',
          priority: 'Low',
          createdDate: DateTime.now().subtract(const Duration(days: 1)),
        ),
      ],
    );
  }

  Future<List<Asset>> getUserAssets({
    int page = 1,
    int limit = 20,
    String? search,
    List<String>? categories,
    List<String>? statuses,
  }) async {
    try {
      await _apiService.initializeToken();

              // 尝试获取真实数据
        try {
          // 获取当前用户信息
          final currentUserResponse = await _apiService.dio.get('/auth/me');
          final currentUserId = currentUserResponse.data['id'];

          // 确保用户ID是整数
          final userIdInt = currentUserId is int ? currentUserId : int.tryParse(currentUserId.toString());
          if (userIdInt == null) {
            throw Exception('无效的用户ID: $currentUserId');
          }

          // 使用现有的资产API获取所有资产，然后筛选用户的资产
          final allAssetsResponse = await _apiService.dio.get('/assets?page=1&limit=1000'); // 获取大量资产
          final List<dynamic> allAssetsData = allAssetsResponse.data['assets'] ?? [];

          // 筛选分配给当前用户的资产
          var userAssetsData = allAssetsData.where((assetJson) {
            final assignedUserId = assetJson['assignedUserId'];
            return assignedUserId != null && assignedUserId.toString() == userIdInt.toString();
          }).toList();

        // 应用搜索筛选
        if (search != null && search.isNotEmpty) {
          userAssetsData = userAssetsData.where((assetJson) {
            final name = assetJson['name']?.toString().toLowerCase() ?? '';
            final assetNumber = assetJson['assetNumber']?.toString().toLowerCase() ?? '';
            final serialNumber = assetJson['serialNumber']?.toString().toLowerCase() ?? '';
            final searchLower = search.toLowerCase();

            return name.contains(searchLower) ||
                   assetNumber.contains(searchLower) ||
                   serialNumber.contains(searchLower);
          }).toList();
        }

        // 应用类别筛选
        if (categories != null && categories.isNotEmpty) {
          userAssetsData = userAssetsData.where((assetJson) {
            final assetCategory = assetJson['category']?.toString().toLowerCase() ?? '';
            return categories.any((cat) => assetCategory.contains(cat.toLowerCase()));
          }).toList();
        }

        // 应用状态筛选
        if (statuses != null && statuses.isNotEmpty) {
          userAssetsData = userAssetsData.where((assetJson) {
            final assetStatus = assetJson['status'];
            if (assetStatus is int) {
              // 状态是数字，需要转换
              final statusMap = {0: 'available', 1: 'assigned', 2: 'maintenance', 3: 'retired'};
              final statusString = statusMap[assetStatus] ?? '';
              return statuses.contains(statusString);
            } else {
              final statusString = assetStatus?.toString().toLowerCase() ?? '';
              return statuses.any((status) => statusString.contains(status.toLowerCase()));
            }
          }).toList();
        }

        // 应用分页
        final startIndex = (page - 1) * limit;
        final endIndex = startIndex + limit;
        final paginatedData = userAssetsData.skip(startIndex).take(limit).toList();

        final assets = paginatedData.map((json) => Asset.fromJson(json)).toList();

        return assets;
      } catch (apiError) {
        // 如果API调用失败，返回模拟的用户资产数据
        return _getMockUserAssets(search: search, categories: categories, statuses: statuses);
      }
    } catch (e) {
      throw Exception('获取用户资产列表失败: $e');
    }
  }

  Future<List<UserTicketSummary>> getUserTickets() async {
    try {
      await _apiService.initializeToken();

      // 暂时使用模拟数据
      // TODO: 实现真实的用户工单列表API
      return _getMockUserDashboardStats().recentTickets;

      // 真实API调用（待后端实现）
      // final response = await _apiService.dio.get('/user/tickets');
      // final List<dynamic> data = response.data;
      // return data.map((json) => UserTicketSummary.fromJson(json)).toList();
    } catch (e) {
      throw Exception('获取用户工单列表失败: $e');
    }
  }

  // 模拟用户资产数据
  List<Asset> _getMockUserAssets({
    String? search,
    List<String>? categories,
    List<String>? statuses,
  }) {
    final mockAssets = <Asset>[
      Asset(
        id: '1',
        name: 'MacBook Pro 16"',
        assetNumber: 'LAP-001',
        category: AssetCategory.laptop,
        status: AssetStatus.assigned,
        brand: 'Apple',
        model: 'MacBook Pro 16" 2023',
        serialNumber: 'MBP16-2023-001',
        location: '办公室A-101',
        assignedTo: '当前用户',
        assignedUserId: '1',
        purchaseDate: DateTime.now().subtract(const Duration(days: 365)),
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
      Asset(
        id: '2',
        name: 'iPhone 14 Pro',
        assetNumber: 'MOB-001',
        category: AssetCategory.phone,
        status: AssetStatus.assigned,
        brand: 'Apple',
        model: 'iPhone 14 Pro',
        serialNumber: 'IP14P-2023-001',
        location: '移动设备',
        assignedTo: '当前用户',
        assignedUserId: '1',
        purchaseDate: DateTime.now().subtract(const Duration(days: 180)),
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
      Asset(
        id: '3',
        name: 'Dell Monitor 27"',
        assetNumber: 'MON-001',
        category: AssetCategory.monitor,
        status: AssetStatus.assigned,
        brand: 'Dell',
        model: 'UltraSharp 27"',
        serialNumber: 'DELL27-2023-001',
        location: '办公室A-101',
        assignedTo: '当前用户',
        assignedUserId: '1',
        purchaseDate: DateTime.now().subtract(const Duration(days: 90)),
        createdAt: DateTime.now().subtract(const Duration(days: 45)),
        updatedAt: DateTime.now().subtract(const Duration(days: 2)),
      ),
      Asset(
        id: '4',
        name: 'Logitech Keyboard',
        assetNumber: 'OTH-001',
        category: AssetCategory.other,
        status: AssetStatus.maintenance,
        brand: 'Logitech',
        model: 'MX Keys',
        serialNumber: 'LG-MXK-001',
        location: '维修中心',
        assignedTo: '当前用户',
        assignedUserId: '1',
        purchaseDate: DateTime.now().subtract(const Duration(days: 200)),
        createdAt: DateTime.now().subtract(const Duration(days: 60)),
        updatedAt: DateTime.now().subtract(const Duration(days: 3)),
      ),
    ];

    // 应用搜索筛选
    var filteredAssets = mockAssets;

    if (search != null && search.isNotEmpty) {
      filteredAssets = filteredAssets.where((asset) =>
        asset.name.toLowerCase().contains(search.toLowerCase()) ||
        asset.assetNumber.toLowerCase().contains(search.toLowerCase()) ||
        (asset.serialNumber?.toLowerCase().contains(search.toLowerCase()) ?? false)
      ).toList();
    }

    // 应用类别筛选
    if (categories != null && categories.isNotEmpty) {
      filteredAssets = filteredAssets.where((asset) =>
        categories.contains(asset.category.toString().split('.').last.toLowerCase())
      ).toList();
    }

    // 应用状态筛选
    if (statuses != null && statuses.isNotEmpty) {
      filteredAssets = filteredAssets.where((asset) =>
        statuses.contains(asset.status.toString().split('.').last.toLowerCase())
      ).toList();
    }

    return filteredAssets;
  }
}
