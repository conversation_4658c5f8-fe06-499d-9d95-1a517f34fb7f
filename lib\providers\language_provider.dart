import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LanguageProvider with ChangeNotifier {
  Locale _currentLocale = const Locale('zh'); // 默认中文
  
  Locale get currentLocale => _currentLocale;
  
  bool get isEnglish => _currentLocale.languageCode == 'en';
  bool get isChinese => _currentLocale.languageCode == 'zh';
  
  // 初始化语言设置
  Future<void> initializeLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    final languageCode = prefs.getString('language_code') ?? 'zh';
    _currentLocale = Locale(languageCode);
    notifyListeners();
  }
  
  // 切换语言
  Future<void> switchLanguage() async {
    final newLanguageCode = _currentLocale.languageCode == 'zh' ? 'en' : 'zh';
    await setLanguage(newLanguageCode);
  }
  
  // 设置特定语言
  Future<void> setLanguage(String languageCode) async {
    if (_currentLocale.languageCode != languageCode) {
      _currentLocale = Locale(languageCode);
      
      // 保存到本地存储
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('language_code', languageCode);
      
      notifyListeners();
    }
  }
  
  // 获取支持的语言列表
  List<Locale> get supportedLocales => const [
    Locale('zh'),
    Locale('en'),
  ];
} 