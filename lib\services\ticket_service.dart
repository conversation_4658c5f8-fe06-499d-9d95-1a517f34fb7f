import '../models/ticket.dart';
import 'api_service.dart';

class TicketService {
  final ApiService _apiService = ApiService();

  Future<List<Ticket>> getUserTickets() async {
    try {
      await _apiService.initializeToken();

      final response = await _apiService.dio.get('/tickets/user');
      final List<dynamic> data = response.data;
      return data.map((json) => Ticket.fromJson(json)).toList();
    } catch (e) {
      throw Exception('获取工单列表失败: $e');
    }
  }

  Future<List<Ticket>> getUserTicketsWithUnreadStatus() async {
    try {
      await _apiService.initializeToken();

      final response = await _apiService.dio.get('/tickets/user/with-unread');
      final List<dynamic> data = response.data;
      return data.map((json) => Ticket.fromJson(json)).toList();
    } catch (e) {
      throw Exception('获取工单列表失败: $e');
    }
  }

  Future<List<Ticket>> getAllTickets() async {
    try {
      await _apiService.initializeToken();

      final response = await _apiService.dio.get('/tickets');
      final List<dynamic> data = response.data;
      return data.map((json) => Ticket.fromJson(json)).toList();
    } catch (e) {
      throw Exception('获取所有工单失败: $e');
    }
  }

  Future<List<Ticket>> getAllTicketsWithUnreadStatus() async {
    try {
      await _apiService.initializeToken();

      final response = await _apiService.dio.get('/tickets/with-unread');
      final List<dynamic> data = response.data;
      return data.map((json) => Ticket.fromJson(json)).toList();
    } catch (e) {
      throw Exception('获取工单列表失败: $e');
    }
  }

  Future<Ticket> getTicket(int id) async {
    try {
      await _apiService.initializeToken();

      final response = await _apiService.dio.get('/tickets/$id');
      return Ticket.fromJson(response.data);
    } catch (e) {
      throw Exception('获取工单详情失败: $e');
    }
  }

  Future<Ticket> createTicket(CreateTicketRequest request) async {
    try {
      await _apiService.initializeToken();

      final response = await _apiService.dio.post('/tickets', data: request.toJson());
      return Ticket.fromJson(response.data);
    } catch (e) {
      throw Exception('创建工单失败: $e');
    }
  }

  Future<void> addComment(int ticketId, String content) async {
    try {
      await _apiService.initializeToken();

      await _apiService.dio.post('/tickets/$ticketId/comments', data: {
        'content': content,
        'isInternal': false,
      });
    } catch (e) {
      throw Exception('添加评论失败: $e');
    }
  }

  Future<void> deleteComment(int commentId) async {
    try {
      await _apiService.initializeToken();

      await _apiService.dio.delete('/tickets/comments/$commentId');
    } catch (e) {
      throw Exception('删除评论失败: $e');
    }
  }

  Future<int> batchDeleteComments(List<int> commentIds) async {
    try {
      await _apiService.initializeToken();

      final response = await _apiService.dio.delete('/tickets/comments/batch', data: {
        'commentIds': commentIds,
      });

      // 返回实际删除的评论数量
      return response.data['deletedCount'] ?? 0;
    } catch (e) {
      throw Exception('批量删除评论失败: $e');
    }
  }

  Future<void> deleteTicket(int ticketId) async {
    try {
      await _apiService.initializeToken();

      await _apiService.dio.delete('/tickets/$ticketId');
    } catch (e) {
      throw Exception('删除工单失败: $e');
    }
  }

  Future<void> updateTicket(int ticketId, Map<String, dynamic> updateData) async {
    try {
      await _apiService.initializeToken();

      await _apiService.dio.put('/tickets/$ticketId', data: updateData);
    } catch (e) {
      throw Exception('更新工单失败: $e');
    }
  }

  Future<void> assignTicket(int ticketId, int assignedToId) async {
    try {
      await _apiService.initializeToken();

      await _apiService.dio.put('/tickets/$ticketId', data: {
        'assignedToId': assignedToId,
      });
    } catch (e) {
      throw Exception('分配工单失败: $e');
    }
  }

  Future<void> markTicketAsViewed(int ticketId) async {
    try {
      await _apiService.initializeToken();

      await _apiService.dio.post('/tickets/$ticketId/mark-viewed');
    } catch (e) {
      throw Exception('标记工单已读失败: $e');
    }
  }

  Future<TicketStatusLimits> getTicketStatusLimits(int ticketId) async {
    try {
      await _apiService.initializeToken();

      final response = await _apiService.dio.get('/tickets/$ticketId/status-limits');
      return TicketStatusLimits.fromJson(response.data);
    } catch (e) {
      throw Exception('获取工单状态限制失败: $e');
    }
  }

  // 模拟数据，用于开发测试
  List<Ticket> _getMockTickets() {
    return [
      Ticket(
        id: 1,
        ticketNumber: 'TKT00001',
        title: '电脑运行缓慢',
        description: '我的电脑最近运行很慢，打开软件需要很长时间，希望能帮忙检查一下。',
        status: TicketStatus.inProgress,
        priority: TicketPriority.medium,
        category: TicketCategory.hardware,
        userId: 1,
        userName: '张三',
        assignedToId: 2,
        assignedToName: 'IT管理员',
        createdDate: DateTime.now().subtract(const Duration(days: 2)),
        updatedDate: DateTime.now().subtract(const Duration(hours: 6)),
        assignedDate: DateTime.now().subtract(const Duration(hours: 6)),
        comments: [
          TicketComment(
            id: 1,
            ticketId: 1,
            userId: 2,
            userName: 'IT管理员',
            content: '我们已经收到您的请求，正在安排技术人员检查您的电脑。',
            createdDate: DateTime.now().subtract(const Duration(hours: 6)),
            isInternal: false,
          ),
        ],
        attachments: [],
      ),
      Ticket(
        id: 2,
        ticketNumber: 'TKT00002',
        title: '软件安装请求',
        description: '需要安装Adobe Photoshop软件用于工作，请帮忙安装。',
        status: TicketStatus.pending,
        priority: TicketPriority.low,
        category: TicketCategory.software,
        userId: 1,
        userName: '张三',
        createdDate: DateTime.now().subtract(const Duration(days: 1)),
        comments: [],
        attachments: [],
      ),
      Ticket(
        id: 3,
        ticketNumber: 'TKT00003',
        title: '网络连接问题',
        description: '办公室网络经常断开，影响工作效率。',
        status: TicketStatus.resolved,
        priority: TicketPriority.high,
        category: TicketCategory.network,
        userId: 1,
        userName: '张三',
        assignedToId: 2,
        assignedToName: 'IT管理员',
        createdDate: DateTime.now().subtract(const Duration(days: 5)),
        updatedDate: DateTime.now().subtract(const Duration(days: 1)),
        assignedDate: DateTime.now().subtract(const Duration(days: 3)),
        resolvedDate: DateTime.now().subtract(const Duration(days: 1)),
        comments: [
          TicketComment(
            id: 2,
            ticketId: 3,
            userId: 2,
            userName: 'IT管理员',
            content: '网络问题已经修复，请测试一下连接是否正常。',
            createdDate: DateTime.now().subtract(const Duration(days: 1)),
            isInternal: false,
          ),
          TicketComment(
            id: 3,
            ticketId: 3,
            userId: 1,
            userName: '张三',
            content: '网络已经恢复正常，谢谢！',
            createdDate: DateTime.now().subtract(const Duration(days: 1)),
            isInternal: false,
          ),
        ],
        attachments: [],
      ),
    ];
  }
}
