import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

/// 布局工具类，用于处理不同语言的文本长度差异
class LayoutUtils {
  /// 获取当前语言是否为英文
  static bool isEnglish(BuildContext context) {
    return AppLocalizations.of(context)?.localeName == 'en';
  }

  /// 获取当前语言是否为中文
  static bool isChinese(BuildContext context) {
    return AppLocalizations.of(context)?.localeName == 'zh';
  }

  /// 根据语言调整按钮的最小宽度
  /// 英文通常需要更多空间
  static double getButtonMinWidth(BuildContext context, {double defaultWidth = 80}) {
    if (isEnglish(context)) {
      return defaultWidth * 1.3; // 英文按钮增加30%宽度
    }
    return defaultWidth;
  }

  /// 根据语言调整文本的字体大小
  /// 英文可能需要稍小的字体以适应更长的文本
  static double getAdaptiveFontSize(BuildContext context, double baseFontSize) {
    if (isEnglish(context)) {
      return baseFontSize * 0.95; // 英文字体稍小5%
    }
    return baseFontSize;
  }

  /// 根据语言调整容器的内边距
  /// 英文可能需要更多的水平内边距
  static EdgeInsets getAdaptivePadding(BuildContext context, EdgeInsets basePadding) {
    if (isEnglish(context)) {
      return EdgeInsets.fromLTRB(
        basePadding.left * 1.2,
        basePadding.top,
        basePadding.right * 1.2,
        basePadding.bottom,
      );
    }
    return basePadding;
  }

  /// 根据语言调整Chip的内边距
  static EdgeInsets getChipPadding(BuildContext context) {
    if (isEnglish(context)) {
      return const EdgeInsets.symmetric(horizontal: 16, vertical: 8);
    }
    return const EdgeInsets.symmetric(horizontal: 12, vertical: 6);
  }

  /// 根据语言调整对话框的宽度
  static double getDialogWidth(BuildContext context, double screenWidth) {
    if (isEnglish(context)) {
      return screenWidth * 0.9; // 英文对话框占屏幕90%宽度
    }
    return screenWidth * 0.8; // 中文对话框占屏幕80%宽度
  }

  /// 根据语言调整文本的最大行数
  static int getMaxLines(BuildContext context, int baseMaxLines) {
    if (isEnglish(context)) {
      return baseMaxLines + 1; // 英文可能需要额外一行
    }
    return baseMaxLines;
  }

  /// 根据语言调整Wrap组件的间距
  static double getWrapSpacing(BuildContext context, {double defaultSpacing = 8}) {
    if (isEnglish(context)) {
      return defaultSpacing * 1.2; // 英文需要更大的间距
    }
    return defaultSpacing;
  }

  /// 根据语言调整表格列宽
  static Map<int, TableColumnWidth> getTableColumnWidths(BuildContext context) {
    if (isEnglish(context)) {
      return const {
        0: FlexColumnWidth(1.5), // 英文标签列更宽
        1: FlexColumnWidth(2.5), // 内容列相应调整
      };
    }
    return const {
      0: FlexColumnWidth(1.0),
      1: FlexColumnWidth(3.0),
    };
  }

  /// 根据语言调整卡片的高度
  static double getCardHeight(BuildContext context, double baseHeight) {
    if (isEnglish(context)) {
      return baseHeight * 1.15; // 英文卡片增加15%高度
    }
    return baseHeight;
  }

  /// 根据语言调整底部弹窗的高度
  static double getBottomSheetHeight(BuildContext context, double screenHeight) {
    if (isEnglish(context)) {
      return screenHeight * 0.75; // 英文底部弹窗占屏幕75%高度
    }
    return screenHeight * 0.65; // 中文底部弹窗占屏幕65%高度
  }

  /// 根据语言调整搜索框的提示文本样式
  static TextStyle getHintTextStyle(BuildContext context, TextStyle baseStyle) {
    if (isEnglish(context)) {
      return baseStyle.copyWith(fontSize: baseStyle.fontSize! * 0.9);
    }
    return baseStyle;
  }

  /// 根据语言调整工具栏按钮的间距
  static double getToolbarButtonSpacing(BuildContext context) {
    if (isEnglish(context)) {
      return 16.0; // 英文工具栏按钮间距更大
    }
    return 12.0;
  }

  /// 根据语言调整筛选芯片的样式
  static BoxDecoration getFilterChipDecoration(BuildContext context, bool isSelected) {
    final borderRadius = isEnglish(context)
        ? BorderRadius.circular(20)
        : BorderRadius.circular(16);

    return BoxDecoration(
      color: isSelected ? Colors.blue[600] : Colors.grey[100],
      borderRadius: borderRadius,
      border: Border.all(
        color: isSelected ? Colors.blue[600]! : Colors.grey[200]!,
        width: 1,
      ),
    );
  }

  /// 根据语言调整日期选择器的样式
  static EdgeInsets getDateSelectorPadding(BuildContext context) {
    if (isEnglish(context)) {
      return const EdgeInsets.symmetric(horizontal: 16, vertical: 18);
    }
    return const EdgeInsets.symmetric(horizontal: 12, vertical: 16);
  }
}
