import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../models/asset.dart';
import '../models/user.dart';
import '../providers/asset_provider.dart';
import '../services/api_service.dart';
import '../config/routes.dart';
import '../widgets/main_layout.dart';
import '../widgets/modern_form_field.dart';
import '../widgets/success_animation.dart';

class AssetFormScreen extends StatefulWidget {
  final String? assetId;
  
  const AssetFormScreen({
    super.key,
    this.assetId,
  });

  @override
  State<AssetFormScreen> createState() => _AssetFormScreenState();
}

class _AssetFormScreenState extends State<AssetFormScreen> {
  final _formKey = GlobalKey<FormState>();
  
  // 表单控制器
  final _nameController = TextEditingController();
  final _assetNumberController = TextEditingController();
  final _brandController = TextEditingController();
  final _modelController = TextEditingController();
  final _serialNumberController = TextEditingController();
  final _assignedToController = TextEditingController();
  final _valueController = TextEditingController();
  final _vendorController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _locationController = TextEditingController();
  
  // 表单状态
  AssetCategory _selectedCategory = AssetCategory.laptop;
  AssetStatus _selectedStatus = AssetStatus.available;
  DateTime? _purchaseDate;
  DateTime? _lastMaintenanceDate;
  DateTime? _nextMaintenanceDate;
  
  bool _isLoading = false;
  bool _isLoadingAsset = true; // 初始状态为加载中
  bool _isLoadingUsers = false;
  Asset? _editingAsset;
  List<User> _users = [];
  String? _selectedUserId; // 选中的用户ID，null表示未分配
  
  @override
  void initState() {
    super.initState();
    print('=== ASSET FORM INIT ===');
    print('Asset ID: ${widget.assetId}');
    print('Is Editing: ${widget.assetId != null}');
    
    // 先加载用户列表，然后再处理资产相关逻辑
    _initializeForm();
  }
  
  Future<void> _initializeForm() async {
    // 加载用户列表
    await _loadUsers();
    
    if (widget.assetId != null) {
      // 编辑模式：加载资产数据
      await _loadAssetForEditing();
    } else {
      // 创建模式：为新资产生成唯一编号
      _generateAssetNumber();
      // 创建模式：根据分配状态设置默认状态
      _updateStatusBasedOnAssignment();
      
      // 创建模式下，用户数据加载完成后立即显示表单
      if (mounted) {
        setState(() {
          _isLoadingAsset = false;
        });
      }
    }
  }
  
  @override
  void dispose() {
    _nameController.dispose();
    _assetNumberController.dispose();
    _brandController.dispose();
    _modelController.dispose();
    _serialNumberController.dispose();
    _assignedToController.dispose();
    _valueController.dispose();
    _vendorController.dispose();
    _descriptionController.dispose();
    _locationController.dispose();
    super.dispose();
  }

  bool get _isEditing => widget.assetId != null;

  Future<void> _loadUsers() async {
    setState(() {
      _isLoadingUsers = true;
    });

    try {
      final apiService = ApiService();
      await apiService.initializeToken(); // 确保token已初始化
      
      print('=== LOADING USERS ===');
      print('API Base URL: ${ApiService.baseUrl}');
      
      final users = await apiService.getUsers();
      print('Successfully loaded ${users.length} users');
      
      setState(() {
        _users = users;
      });
    } catch (e) {
      print('加载用户列表失败: $e');
      _showError(AppLocalizations.of(context)!.loadUserListFailed(e.toString()));
      
      // 如果API失败，使用空列表继续
      setState(() {
        _users = [];
      });
    } finally {
      setState(() {
        _isLoadingUsers = false;
      });
    }
  }

  Future<void> _loadAssetForEditing() async {
    setState(() {
      _isLoadingAsset = true;
    });

    try {
      final assetProvider = Provider.of<AssetProvider>(context, listen: false);
      final asset = await assetProvider.getAsset(widget.assetId!);
      
      if (asset != null) {
        // 先设置资产数据，再更新UI状态
        _editingAsset = asset;
        _populateForm(asset);
        
        // 确保数据完全填充后再显示表单
        await Future.delayed(const Duration(milliseconds: 100));
        
        if (mounted) {
          setState(() {
            _isLoadingAsset = false;
          });
        }
      } else {
        _showError(AppLocalizations.of(context)!.assetNotFound);
        if (mounted) {
          context.pop();
        }
      }
    } catch (e) {
      _showError(AppLocalizations.of(context)!.loadAssetFailed(e.toString()));
      if (mounted) {
        setState(() {
          _isLoadingAsset = false;
        });
      }
    }
  }

  void _populateForm(Asset asset) {
    _nameController.text = asset.name;
    _assetNumberController.text = asset.assetNumber;
    _brandController.text = asset.brand ?? '';
    _modelController.text = asset.model ?? '';
    _serialNumberController.text = asset.serialNumber ?? '';
    _assignedToController.text = asset.assignedTo ?? '';
    _valueController.text = asset.value?.toString() ?? '';
    _vendorController.text = asset.vendor ?? '';
    _descriptionController.text = asset.description ?? '';
    _locationController.text = asset.location ?? '';
    
    // 确保枚举值是有效的
    try {
      _selectedCategory = asset.category;
    } catch (e) {
      print('Category parsing error: $e');
      _selectedCategory = AssetCategory.other;
    }
    
    try {
      _selectedStatus = asset.status;
    } catch (e) {
      print('Status parsing error: $e');
      _selectedStatus = AssetStatus.available;
    }
    
    _purchaseDate = asset.purchaseDate;
    _lastMaintenanceDate = asset.lastMaintenanceDate;
    _nextMaintenanceDate = asset.nextMaintenanceDate;
    
    // 设置选中的用户ID
    if (asset.assignedUserId != null && asset.assignedUserId!.isNotEmpty) {
      _selectedUserId = asset.assignedUserId;
    } else if (asset.assignedTo != null && asset.assignedTo!.isNotEmpty && _users.isNotEmpty) {
      // 如果没有assignedUserId但有assignedTo，尝试根据assignedTo找到对应的用户
      try {
        final assignedUser = _users.firstWhere(
          (user) => user.username == asset.assignedTo || user.fullName == asset.assignedTo,
          orElse: () => User(
            id: 0,
            username: '',
            email: '',
            role: '',
            fullName: null,
            department: null,
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            assignedAssetsCount: 0,
          ),
        );
        if (assignedUser.id != 0) {
          _selectedUserId = assignedUser.id.toString();
        } else {
          _selectedUserId = null;
        }
      } catch (e) {
        print('User assignment error: $e');
        _selectedUserId = null;
      }
    } else {
      _selectedUserId = null;
    }
    
    // 根据分配状态调整资产状态
    _updateStatusBasedOnAssignment();
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // 根据选中的用户ID获取用户名
      String? assignedToName;
      String? assignedUserId;
      if (_selectedUserId != null) {
        final selectedUser = _users.firstWhere(
          (user) => user.id.toString() == _selectedUserId,
          orElse: () => User(
            id: 0,
            username: '',
            email: '',
            role: '',
            fullName: null,
            department: null,
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            assignedAssetsCount: 0,
          ),
        );
        if (selectedUser.id != 0) {
          assignedToName = selectedUser.fullName ?? selectedUser.username;
          assignedUserId = selectedUser.id.toString();
        }
      }

      print('=== 提交资产表单 ===');
      print('资产ID: ${_editingAsset?.id}');
      print('选中状态: $_selectedStatus');
      print('分配用户ID: $_selectedUserId');
      print('分配用户名: $assignedToName');
      
      // 创建资产对象前先打印状态
      print('创建Asset对象前的状态: $_selectedStatus');

      final asset = Asset(
        id: _editingAsset?.id,
        name: _nameController.text.trim(),
        assetNumber: _assetNumberController.text.trim(), // 使用生成的资产编号
        category: _selectedCategory,
        status: _selectedStatus,
        brand: _brandController.text.trim().isEmpty ? null : _brandController.text.trim(),
        model: _modelController.text.trim().isEmpty ? null : _modelController.text.trim(),
        serialNumber: _serialNumberController.text.trim().isEmpty ? null : _serialNumberController.text.trim(),
        assignedTo: assignedToName,
        assignedUserId: assignedUserId,
        value: _valueController.text.trim().isEmpty ? null : double.tryParse(_valueController.text.trim()),
        vendor: _vendorController.text.trim().isEmpty ? null : _vendorController.text.trim(),
        description: _descriptionController.text.trim().isEmpty ? null : _descriptionController.text.trim(),
        location: _locationController.text.trim().isEmpty ? null : _locationController.text.trim(),
        purchaseDate: _purchaseDate,
        lastMaintenanceDate: _lastMaintenanceDate,
        nextMaintenanceDate: _nextMaintenanceDate,
      );

      // 打印Asset对象的状态和JSON数据
      print('Asset对象的状态: ${asset.status}');
      print('Asset toJson: ${asset.toJson()}');

      final assetProvider = Provider.of<AssetProvider>(context, listen: false);
      bool success;
      
      if (_isEditing) {
        success = await assetProvider.updateAsset(widget.assetId!, asset);
      } else {
        success = await assetProvider.createAsset(asset);
      }

      if (success) {
        if (mounted) {
          // 显示现代化的成功动画
          SuccessAnimationOverlay.show(
            context,
            title: _isEditing ? AppLocalizations.of(context)!.updateSuccess : AppLocalizations.of(context)!.createSuccess,
            message: _isEditing ? AppLocalizations.of(context)!.assetUpdatedSuccess : AppLocalizations.of(context)!.assetCreatedSuccess,
            onComplete: () {
              // 成功后返回资产列表并强制刷新
              context.go(AppRoutes.assetList);
              // 延迟一下再刷新，确保页面已经加载
              Future.delayed(const Duration(milliseconds: 100), () {
                if (mounted) {
                  final assetProvider = Provider.of<AssetProvider>(context, listen: false);
                  assetProvider.refreshAssets();
                }
              });
            },
          );
        }
      } else {
        _showError(assetProvider.errorMessage ?? (_isEditing ? AppLocalizations.of(context)!.updateAssetFailed : AppLocalizations.of(context)!.createAssetFailed));
      }
    } catch (e) {
      _showError(AppLocalizations.of(context)!.operationFailed(e.toString()));
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _selectDate(BuildContext context, String fieldName) async {
    DateTime? currentDate;
    switch (fieldName) {
      case 'purchase':
        currentDate = _purchaseDate;
        break;
      case 'lastMaintenance':
        currentDate = _lastMaintenanceDate;
        break;
      case 'nextMaintenance':
        currentDate = _nextMaintenanceDate;
        break;
    }

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: currentDate ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2030),
    );

    if (picked != null) {
      setState(() {
        switch (fieldName) {
          case 'purchase':
            _purchaseDate = picked;
            break;
          case 'lastMaintenance':
            _lastMaintenanceDate = picked;
            break;
          case 'nextMaintenance':
            _nextMaintenanceDate = picked;
            break;
        }
      });
    }
  }

  String _getCategoryDisplayName(AssetCategory category) {
    switch (category) {
      case AssetCategory.laptop:
        return AppLocalizations.of(context)!.categoryLaptop;
      case AssetCategory.desktop:
        return AppLocalizations.of(context)!.categoryDesktop;
      case AssetCategory.monitor:
        return AppLocalizations.of(context)!.categoryMonitor;
      case AssetCategory.printer:
        return AppLocalizations.of(context)!.categoryPrinter;
      case AssetCategory.phone:
        return AppLocalizations.of(context)!.categoryMobile;
      case AssetCategory.tablet:
        return AppLocalizations.of(context)!.categoryTablet;
      case AssetCategory.server:
        return AppLocalizations.of(context)!.categoryServer;
      case AssetCategory.network:
        return AppLocalizations.of(context)!.categoryNetwork;
      case AssetCategory.other:
        return AppLocalizations.of(context)!.categoryOther;
    }
  }

  String _getStatusDisplayName(AssetStatus status) {
    switch (status) {
      case AssetStatus.available:
        return AppLocalizations.of(context)!.statusUnassigned;
      case AssetStatus.assigned:
        return AppLocalizations.of(context)!.statusAssigned;
      case AssetStatus.maintenance:
        return AppLocalizations.of(context)!.statusMaintenance;
      case AssetStatus.retired:
        return AppLocalizations.of(context)!.statusRetired;
    }
  }

  Color _getStatusColor(AssetStatus status) {
    switch (status) {
      case AssetStatus.available:
        return Colors.green;
      case AssetStatus.assigned:
        return Colors.blue;
      case AssetStatus.maintenance:
        return Colors.orange;
      case AssetStatus.retired:
        return Colors.red;
    }
  }

  Color _getCategoryColor(AssetCategory category) {
    switch (category) {
      case AssetCategory.laptop:
        return const Color(0xFF2196F3);
      case AssetCategory.desktop:
        return const Color(0xFF4CAF50);
      case AssetCategory.monitor:
        return const Color(0xFFFF9800);
      case AssetCategory.printer:
        return const Color(0xFF9C27B0);
      case AssetCategory.phone:
        return const Color(0xFFF44336);
      case AssetCategory.tablet:
        return const Color(0xFF00BCD4);
      case AssetCategory.server:
        return const Color(0xFF795548);
      case AssetCategory.network:
        return const Color(0xFF607D8B);
      case AssetCategory.other:
        return const Color(0xFF9E9E9E);
    }
  }

  Future<void> _generateAssetNumber() async {
    try {
      final assetProvider = Provider.of<AssetProvider>(context, listen: false);
      final nextNumber = await assetProvider.getNextAssetNumber(_selectedCategory);
      if (nextNumber.isNotEmpty) {
        setState(() {
          _assetNumberController.text = nextNumber;
        });
      }
    } catch (e) {
      print('Failed to generate asset number: $e');
      // 如果API失败，使用默认逻辑
      _generateDefaultAssetNumber();
    }
  }
  
  void _generateDefaultAssetNumber() {
    final prefix = _getCategoryPrefix(_selectedCategory);
    final now = DateTime.now();
    final year = now.year;
    final month = now.month.toString().padLeft(2, '0');
    final day = now.day.toString().padLeft(2, '0');
    final randomNum = (DateTime.now().millisecondsSinceEpoch % 999 + 1).toString().padLeft(3, '0');
    _assetNumberController.text = '$prefix$year$month$day$randomNum';
  }
  
  String _getCategoryPrefix(AssetCategory category) {
    switch (category) {
      case AssetCategory.laptop:
        return 'NB';
      case AssetCategory.desktop:
        return 'PC';
      case AssetCategory.monitor:
        return 'MON';
      case AssetCategory.printer:
        return 'PRT';
      case AssetCategory.phone:
        return 'PHN';
      case AssetCategory.tablet:
        return 'TAB';
      case AssetCategory.server:
        return 'SVR';
      case AssetCategory.network:
        return 'NET';
      case AssetCategory.other:
        return 'OTH';
    }
  }

  String _getValidationMessage(String label) {
    // 根据标签返回相应的验证消息
    if (label == AppLocalizations.of(context)!.assetName) {
      return AppLocalizations.of(context)!.pleaseEnterAssetName;
    } else if (label == AppLocalizations.of(context)!.brand) {
      return AppLocalizations.of(context)!.pleaseEnterBrand;
    } else if (label == AppLocalizations.of(context)!.model) {
      return AppLocalizations.of(context)!.pleaseEnterModel;
    } else if (label == AppLocalizations.of(context)!.serialNumber) {
      return AppLocalizations.of(context)!.pleaseEnterSerialNumber;
    } else if (label == AppLocalizations.of(context)!.vendor) {
      return AppLocalizations.of(context)!.pleaseEnterVendor;
    } else if (label == AppLocalizations.of(context)!.location) {
      return AppLocalizations.of(context)!.pleaseEnterLocation;
    } else {
      // 通用消息
      return AppLocalizations.of(context)!.pleaseEnterAssetName;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoadingAsset || (_isEditing && _editingAsset == null)) {
      return MainLayout(
        currentRoute: _isEditing ? '/assets/${widget.assetId}/edit' : AppRoutes.assetCreate,
        showBackButton: true,
        title: _isEditing ? AppLocalizations.of(context)!.editAsset : AppLocalizations.of(context)!.createAsset,
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.grey[50]!,
                Colors.white,
              ],
            ),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF2196F3)),
                ),
                const SizedBox(height: 16),
                Text(
                  AppLocalizations.of(context)!.loadingAssetInfo,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return MainLayout(
      currentRoute: _isEditing ? '/assets/${widget.assetId}/edit' : AppRoutes.assetCreate,
      showBackButton: true,
      title: _isEditing ? AppLocalizations.of(context)!.editAsset : AppLocalizations.of(context)!.createAsset,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.grey[50]!,
              Colors.white,
            ],
          ),
        ),
        child: AnimatedSwitcher(
          duration: const Duration(milliseconds: 300),
          child: Form(
            key: _formKey,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                // 基本信息卡片
                ModernSectionCard(
                  title: AppLocalizations.of(context)!.basicInfo,
                  icon: Icons.info_outline_rounded,
                  iconColor: const Color(0xFF2196F3),
                  children: [
                    ModernTextFormField(
                      controller: _nameController,
                      label: AppLocalizations.of(context)!.assetName,
                      hint: AppLocalizations.of(context)!.assetNameHint,
                      required: true,
                      prefixIcon: Icons.inventory_2_rounded,
                    ),
                    const SizedBox(height: 20),
                    ModernTextFormField(
                      controller: _assetNumberController,
                      label: AppLocalizations.of(context)!.assetNumber,
                      hint: AppLocalizations.of(context)!.assetNumberHint,
                      required: true,
                      prefixIcon: Icons.tag_rounded,
                      readOnly: true,
                    ),
                    const SizedBox(height: 20),
                    Column(
                      children: [
                        ModernDropdownField<AssetCategory>(
                          value: _selectedCategory,
                          label: AppLocalizations.of(context)!.category,
                          items: AssetCategory.values,
                          itemBuilder: (category) => _getCategoryDisplayName(category),
                          onChanged: (value) {
                            setState(() {
                              _selectedCategory = value!;
                            });
                            // 如果不是编辑模式，自动重新生成资产编号
                            if (!_isEditing) {
                              _generateAssetNumber();
                            }
                          },
                          prefixIcon: Icon(
                            Icons.category_rounded,
                            color: _getCategoryColor(_selectedCategory),
                          ),
                        ),
                        const SizedBox(height: 20),
                        ModernDropdownField<AssetStatus>(
                          value: _selectedStatus,
                          label: AppLocalizations.of(context)!.status,
                          items: _getAvailableStatusOptions(),
                          itemBuilder: (status) => _getStatusDisplayName(status),
                          onChanged: (value) {
                            print('=== 状态下拉框变化 ===');
                            print('新状态: $value');
                            setState(() {
                              _selectedStatus = value!;
                            });
                          },
                          prefixIcon: Icon(
                            Icons.circle,
                            color: _getStatusColor(_selectedStatus),
                            size: 16,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // 详细信息卡片
                ModernSectionCard(
                  title: AppLocalizations.of(context)!.detailedInfo,
                  icon: Icons.details_rounded,
                  iconColor: const Color(0xFF9C27B0),
                  children: [
                    Row(
                      children: [
                        Expanded(
                          flex: 1,
                          child: ModernTextFormField(
                            controller: _brandController,
                            label: AppLocalizations.of(context)!.brand,
                            hint: AppLocalizations.of(context)!.brandHint,
                            prefixIcon: Icons.business_rounded,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          flex: 1,
                          child: ModernTextFormField(
                            controller: _modelController,
                            label: AppLocalizations.of(context)!.model,
                            hint: AppLocalizations.of(context)!.modelHint,
                            prefixIcon: Icons.model_training_rounded,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    ModernTextFormField(
                      controller: _serialNumberController,
                      label: AppLocalizations.of(context)!.serialNumber,
                      hint: AppLocalizations.of(context)!.serialNumberHint,
                      prefixIcon: Icons.confirmation_number_rounded,
                    ),
                    const SizedBox(height: 20),
                    Row(
                      children: [
                        Expanded(
                          flex: 1,
                          child: ModernTextFormField(
                            controller: _vendorController,
                            label: AppLocalizations.of(context)!.vendor,
                            hint: AppLocalizations.of(context)!.vendorHint,
                            prefixIcon: Icons.store_rounded,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          flex: 1,
                          child: ModernTextFormField(
                            controller: _locationController,
                            label: AppLocalizations.of(context)!.location,
                            hint: AppLocalizations.of(context)!.locationHint,
                            prefixIcon: Icons.location_on_rounded,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // 分配信息卡片
                ModernSectionCard(
                  title: AppLocalizations.of(context)!.assignmentInfo,
                  icon: Icons.person_rounded,
                  iconColor: const Color(0xFF4CAF50),
                  children: [
                    _buildModernUserDropdownField(),
                  ],
                ),

                const SizedBox(height: 20),

                // 财务信息卡片
                ModernSectionCard(
                  title: AppLocalizations.of(context)!.financialInfo,
                  icon: Icons.attach_money_rounded,
                  iconColor: const Color(0xFFFF9800),
                  children: [
                    Row(
                      children: [
                        Expanded(
                          flex: 1,
                          child: ModernTextFormField(
                            controller: _valueController,
                            label: AppLocalizations.of(context)!.value,
                            hint: AppLocalizations.of(context)!.valueHint,
                            prefixIcon: Icons.monetization_on_rounded,
                            keyboardType: TextInputType.number,
                            inputFormatters: [
                              FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                            ],
                            validator: (value) {
                              if (value != null && value.isNotEmpty) {
                                if (double.tryParse(value) == null) {
                                  return AppLocalizations.of(context)!.pleaseEnterValidValue;
                                }
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          flex: 1,
                          child: ModernDateField(
                            label: AppLocalizations.of(context)!.purchaseDate,
                            value: _purchaseDate,
                            onTap: () => _selectDate(context, 'purchase'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // 维护信息卡片
                ModernSectionCard(
                  title: AppLocalizations.of(context)!.maintenanceInfo,
                  icon: Icons.build_rounded,
                  iconColor: const Color(0xFFF44336),
                  children: [
                    Row(
                      children: [
                        Expanded(
                          flex: 1,
                          child: ModernDateField(
                            label: AppLocalizations.of(context)!.lastMaintenanceDate,
                            value: _lastMaintenanceDate,
                            onTap: () => _selectDate(context, 'lastMaintenance'),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          flex: 1,
                          child: ModernDateField(
                            label: AppLocalizations.of(context)!.nextMaintenanceDate,
                            value: _nextMaintenanceDate,
                            onTap: () => _selectDate(context, 'nextMaintenance'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // 描述信息卡片
                ModernSectionCard(
                  title: AppLocalizations.of(context)!.additionalInfo,
                  icon: Icons.notes_rounded,
                  iconColor: const Color(0xFF607D8B),
                  children: [
                    ModernTextFormField(
                      controller: _descriptionController,
                      label: AppLocalizations.of(context)!.description,
                      hint: AppLocalizations.of(context)!.descriptionHint,
                      maxLines: 3,
                      prefixIcon: Icons.description_rounded,
                    ),
                  ],
                ),

                  const SizedBox(height: 100), // 为浮动按钮留出空间
                ],
              ),
            ),
          ),
        ),
      ),
      floatingActionButton: _buildModernSubmitButton(),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, size: 20),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildTextFormField({
    required TextEditingController controller,
    required String label,
    String? hint,
    bool required = false,
    IconData? prefixIcon,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    String? Function(String?)? validator,
    int maxLines = 1,
    bool readOnly = false,
    Widget? suffixIcon,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label + (required ? ' *' : ''),
        hintText: hint,
        prefixIcon: prefixIcon != null ? Icon(prefixIcon) : null,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        suffixIcon: suffixIcon,
      ),
      keyboardType: keyboardType,
      inputFormatters: inputFormatters,
      maxLines: maxLines,
      readOnly: readOnly,
      validator: validator ?? (required ? (value) {
        if (value == null || value.trim().isEmpty) {
          return _getValidationMessage(label);
        }
        return null;
      } : null),
    );
  }

  Widget _buildDropdownField<T>({
    required T value,
    required String label,
    required List<T> items,
    required String Function(T) itemBuilder,
    required void Function(T?) onChanged,
    Widget? prefixIcon,
  }) {
    return DropdownButtonFormField<T>(
      value: value,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: prefixIcon,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      items: items.map((item) {
        return DropdownMenuItem<T>(
          value: item,
          child: Text(itemBuilder(item)),
        );
      }).toList(),
      onChanged: onChanged,
    );
  }

  Widget _buildDateField({
    required String label,
    required DateTime? value,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: label,
          prefixIcon: const Icon(Icons.calendar_today),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: Text(
          value != null 
            ? DateFormat('yyyy-MM-dd').format(value)
            : AppLocalizations.of(context)!.selectDate,
          style: TextStyle(
            color: value != null ? null : Colors.grey[600],
          ),
        ),
      ),
    );
  }

  Widget _buildUserDropdownField() {
    if (_isLoadingUsers) {
      return Container(
        height: 56,
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey),
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    // 创建选项列表，包含"未分配"选项
    final List<DropdownMenuItem<String?>> items = [
      DropdownMenuItem<String?>(
        value: null,
        child: Text(AppLocalizations.of(context)!.statusUnassigned),
      ),
    ];

    // 添加用户选项，只显示Normal User角色的用户，按用户名排序
    final normalUsers = _users.where((user) => user.isNormalUser).toList();
    normalUsers.sort((a, b) => a.username.compareTo(b.username));
    
    for (final user in normalUsers) {
      items.add(
        DropdownMenuItem<String?>(
          value: user.id.toString(),
          child: Text(user.fullName ?? user.username),
        ),
      );
    }

    return DropdownButtonFormField<String?>(
      value: _selectedUserId,
      decoration: InputDecoration(
        labelText: AppLocalizations.of(context)!.assignedToLabel,
        prefixIcon: const Icon(Icons.person_outline),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      items: items,
      onChanged: (value) {
        setState(() {
          final oldUserId = _selectedUserId;
          _selectedUserId = value;
          // 只在分配状态发生实际改变时调整资产状态
          _updateStatusBasedOnAssignmentChange(oldUserId, value);
        });
      },
      hint: Text(AppLocalizations.of(context)!.assignedUserHint),
    );
  }

  Widget _buildModernSubmitButton() {
    return Container(
      width: 64,
      height: 64,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [
            Color(0xFF2196F3),
            Color(0xFF1976D2),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(32),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF2196F3).withOpacity(0.4),
            spreadRadius: 0,
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _isLoading ? null : _submitForm,
          borderRadius: BorderRadius.circular(32),
          child: Center(
            child: _isLoading
                ? const SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : Icon(
                    _isEditing ? Icons.save_rounded : Icons.add_rounded,
                    color: Colors.white,
                    size: 28,
                  ),
          ),
        ),
      ),
    );
  }

  Widget _buildModernUserDropdownField() {
    if (_isLoadingUsers) {
      return Container(
        height: 72,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              spreadRadius: 0,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                const SizedBox(width: 12),
                Text(AppLocalizations.of(context)!.loadingAssetInfo),
              ],
            ),
          ),
        ),
      );
    }

    // 创建选项列表，包含"未分配"选项
    final List<String?> items = [null];

    // 添加用户选项，只显示Normal User角色的用户，按用户名排序
    final normalUsers = _users.where((user) => user.isNormalUser).toList();
    normalUsers.sort((a, b) => a.username.compareTo(b.username));
    
    for (final user in normalUsers) {
      items.add(user.id.toString());
    }

    return ModernDropdownField<String?>(
      value: _selectedUserId,
      label: AppLocalizations.of(context)!.assignedToLabel,
      items: items,
      itemBuilder: (value) {
        if (value == null) return AppLocalizations.of(context)!.statusUnassigned;
        final user = _users.firstWhere(
          (u) => u.id.toString() == value,
          orElse: () => User(
            id: 0,
            username: AppLocalizations.of(context)!.unknown,
            email: '',
            role: '',
            fullName: null,
            department: null,
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            assignedAssetsCount: 0,
          ),
        );
        return user.fullName ?? user.username;
      },
      onChanged: (value) {
        setState(() {
          final oldUserId = _selectedUserId;
          _selectedUserId = value;
          // 只在分配状态发生实际改变时调整资产状态
          _updateStatusBasedOnAssignmentChange(oldUserId, value);
        });
      },
      prefixIcon: Icon(
        _selectedUserId == null ? Icons.person_outline_rounded : Icons.person_rounded,
        color: _selectedUserId == null ? Colors.grey[600] : const Color(0xFF4CAF50),
      ),
    );
  }

  void _updateStatusBasedOnAssignment() {
    // 只在初始化时设置默认状态
    if (!_isEditing) {
      // 创建模式：设置默认状态
      if (_selectedUserId == null) {
        _selectedStatus = AssetStatus.available;
      } else {
        _selectedStatus = AssetStatus.assigned;
      }
    }
    
    // 确保当前状态在可用选项中
    _validateCurrentStatus();
  }

  void _updateStatusBasedOnAssignmentChange(String? oldUserId, String? newUserId) {
    // 只在分配状态实际发生改变时调整状态
    final wasAssigned = oldUserId != null;
    final isNowAssigned = newUserId != null;
    
    if (wasAssigned != isNowAssigned) {
      // 分配状态发生了改变
      if (!isNowAssigned && _selectedStatus == AssetStatus.assigned) {
        // 从分配给用户改为未分配，且当前状态是已分配 → 改为未分配
        _selectedStatus = AssetStatus.available;
      } else if (isNowAssigned && _selectedStatus == AssetStatus.available) {
        // 从未分配改为分配给用户，且当前状态是未分配 → 改为已分配
        _selectedStatus = AssetStatus.assigned;
      }
      // 如果用户选择了维护中或已报废，保持不变
    }
    
    // 确保当前状态在可用选项中
    _validateCurrentStatus();
  }
  
  void _validateCurrentStatus() {
    final availableOptions = _getAvailableStatusOptions();
    if (!availableOptions.contains(_selectedStatus)) {
      // 如果当前状态不在可用选项中，选择第一个可用选项
      _selectedStatus = availableOptions.first;
    }
  }

  List<AssetStatus> _getAvailableStatusOptions() {
    if (_isEditing) {
      // 编辑模式：根据当前状态和分配状态决定可用选项
      if (_selectedUserId == null) {
        // 未分配：显示未分配、维护中、已报废
        return [AssetStatus.available, AssetStatus.maintenance, AssetStatus.retired];
      } else {
        // 已分配：显示已分配、维护中、已报废
        return [AssetStatus.assigned, AssetStatus.maintenance, AssetStatus.retired];
      }
    } else {
      // 创建模式：根据分配状态决定可用选项
      if (_selectedUserId == null) {
        // 未分配：显示未分配、维护中、已报废
        return [AssetStatus.available, AssetStatus.maintenance, AssetStatus.retired];
      } else {
        // 已分配：显示已分配、维护中、已报废
        return [AssetStatus.assigned, AssetStatus.maintenance, AssetStatus.retired];
      }
    }
  }
}
