import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../providers/auth_provider.dart';
import '../providers/dashboard_provider.dart';
import '../config/routes.dart';
import '../config/api_config.dart';
import '../widgets/dashboard_stats_card.dart';
import '../widgets/dashboard_chart.dart';
import '../widgets/main_layout.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadDashboardData();
    });
  }

  // 获取完整的头像URL
  String _getFullAvatarUrl(String avatarUrl) {
    if (avatarUrl.startsWith('http://') || avatarUrl.startsWith('https://')) {
      return avatarUrl;
    } else {
      // 相对URL，需要添加基础URL
      return '${ApiConfig.baseUrl}$avatarUrl';
    }
  }

  Future<void> _loadDashboardData() async {
    final dashboardProvider = Provider.of<DashboardProvider>(context, listen: false);
    await dashboardProvider.fetchDashboardStats();
  }

  Future<void> _handleRefresh() async {
    await _loadDashboardData();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    return MainLayout(
      currentRoute: AppRoutes.dashboard,
      child: RefreshIndicator(
        onRefresh: _handleRefresh,
        child: Consumer<DashboardProvider>(
          builder: (context, dashboardProvider, child) {
            if (dashboardProvider.isLoading && dashboardProvider.stats == null) {
              return const Center(
                child: CircularProgressIndicator(),
              );
            }

            if (dashboardProvider.errorMessage != null) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Colors.red.shade400,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      l10n.loadFailed,
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      dashboardProvider.errorMessage!,
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _handleRefresh,
                      child: Text(l10n.retry),
                    ),
                  ],
                ),
              );
            }

            final stats = dashboardProvider.stats;
            if (stats == null) {
              return Center(
                child: Text(l10n.noData),
              );
            }

            return SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 欢迎信息
                  Consumer<AuthProvider>(
                    builder: (context, authProvider, child) {
                      final user = authProvider.currentUser;
                      return Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Row(
                            children: [
                              CircleAvatar(
                                backgroundColor: Theme.of(context).colorScheme.primary,
                                backgroundImage: user?.avatarUrl != null
                                    ? NetworkImage(_getFullAvatarUrl(user!.avatarUrl!))
                                    : null,
                                child: user?.avatarUrl == null
                                    ? Text(
                                        user?.fullName?.substring(0, 1).toUpperCase() ?? 
                                        user?.username.substring(0, 1).toUpperCase() ?? 'U',
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      )
                                    : null,
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      '${l10n.welcomeBack}${user?.fullName ?? user?.username ?? l10n.user}',
                                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    Text(
                                      '${l10n.role}: ${user?.role ?? l10n.unknown}',
                                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),

                  const SizedBox(height: 16),

                  // 统计卡片
                  Text(
                    l10n.assetOverview,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  
                  Column(
                    children: [
                      // 第一行：总资产和已报废
                      Row(
                        children: [
                          Expanded(
                            child: DashboardStatsCard(
                              title: l10n.totalAssets,
                              value: stats.totalAssets.toString(),
                              icon: Icons.inventory_2,
                              color: Colors.blue,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: DashboardStatsCard(
                              title: l10n.retired,
                              value: stats.retiredAssets.toString(),
                              icon: Icons.delete_forever,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      // 第二行：维护中、未分配和已分配
                      Row(
                        children: [
                          Expanded(
                            child: DashboardStatsCard(
                              title: l10n.maintenance,
                              value: stats.maintenanceAssets.toString(),
                              icon: Icons.build,
                              color: Colors.red,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: DashboardStatsCard(
                              title: l10n.unassigned,
                              value: stats.unassignedAssets.toString(),
                              icon: Icons.check_circle,
                              color: Colors.orange,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: DashboardStatsCard(
                              title: l10n.assigned,
                              value: stats.assignedAssets.toString(),
                              icon: Icons.person,
                              color: Colors.green,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // 用户统计
                  Text(
                    l10n.userOverview,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  
                  Row(
                    children: [
                      Expanded(
                        child: DashboardStatsCard(
                          title: l10n.totalUsers,
                          value: stats.totalUsers.toString(),
                          icon: Icons.people,
                          color: Colors.purple,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: DashboardStatsCard(
                          title: l10n.administrators,
                          value: stats.adminUsers.toString(),
                          icon: Icons.admin_panel_settings,
                          color: Colors.indigo,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: DashboardStatsCard(
                          title: l10n.regularUsers,
                          value: stats.regularUsers.toString(),
                          icon: Icons.person,
                          color: Colors.teal,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // 图表
                  Text(
                    l10n.assetDistribution,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  
                  DashboardChart(stats: stats),

                  const SizedBox(height: 24),

                  // 快速操作
                  Text(
                    l10n.quickActions,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  
                  Column(
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: () => context.go(AppRoutes.assetList),
                              icon: const Icon(Icons.list),
                              label: Text(l10n.viewAssets),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: () => context.go(AppRoutes.assetCreate),
                              icon: const Icon(Icons.add),
                              label: Text(l10n.addAsset),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          onPressed: () => context.go(AppRoutes.activityLog),
                          icon: const Icon(Icons.history),
                          label: Text(l10n.viewActivityLog),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange[600],
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Future<void> _handleLogout() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    await authProvider.logout();
  }
}
