class Ticket {
  final int id;
  final String ticketNumber;
  final String title;
  final String description;
  final String status;
  final String priority;
  final String category;
  final int userId;
  final String userName;
  final int? assignedToId;
  final String? assignedToName;
  final DateTime createdDate;
  final DateTime? updatedDate;
  final DateTime? assignedDate;
  final DateTime? resolvedDate;
  final DateTime? closedDate;
  final DateTime? lastStatusChangeDate;
  final String? previousStatus;
  final int rollbackCount;
  final DateTime? lastRollbackDate;
  final List<TicketComment> comments;
  final List<TicketAttachment> attachments;
  final int unreadCommentsCount;
  final bool hasUnreadComments;

  Ticket({
    required this.id,
    required this.ticketNumber,
    required this.title,
    required this.description,
    required this.status,
    required this.priority,
    required this.category,
    required this.userId,
    required this.userName,
    this.assignedToId,
    this.assignedToName,
    required this.createdDate,
    this.updatedDate,
    this.assignedDate,
    this.resolvedDate,
    this.closedDate,
    this.lastStatusChangeDate,
    this.previousStatus,
    this.rollbackCount = 0,
    this.lastRollbackDate,
    required this.comments,
    required this.attachments,
    this.unreadCommentsCount = 0,
    this.hasUnreadComments = false,
  });

  factory Ticket.fromJson(Map<String, dynamic> json) {
    return Ticket(
      id: json['id'],
      ticketNumber: json['ticketNumber'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      status: json['status'] ?? 'Pending',
      priority: json['priority'] ?? 'Medium',
      category: json['category'] ?? 'Other',
      userId: json['userId'] ?? 0,
      userName: json['userName'] ?? '',
      assignedToId: json['assignedToId'],
      assignedToName: json['assignedToName'],
      createdDate: DateTime.parse(json['createdDate']).toUtc(),
      updatedDate: json['updatedDate'] != null
          ? DateTime.parse(json['updatedDate']).toUtc()
          : null,
      assignedDate: json['assignedDate'] != null
          ? DateTime.parse(json['assignedDate']).toUtc()
          : null,
      resolvedDate: json['resolvedDate'] != null
          ? DateTime.parse(json['resolvedDate']).toUtc()
          : null,
      closedDate: json['closedDate'] != null
          ? DateTime.parse(json['closedDate']).toUtc()
          : null,
      lastStatusChangeDate: json['lastStatusChangeDate'] != null
          ? DateTime.parse(json['lastStatusChangeDate']).toUtc()
          : null,
      previousStatus: json['previousStatus'],
      rollbackCount: json['rollbackCount'] ?? 0,
      lastRollbackDate: json['lastRollbackDate'] != null
          ? DateTime.parse(json['lastRollbackDate']).toUtc()
          : null,
      comments: (json['comments'] as List<dynamic>?)
          ?.map((item) => TicketComment.fromJson(item))
          .toList() ?? [],
      attachments: (json['attachments'] as List<dynamic>?)
          ?.map((item) => TicketAttachment.fromJson(item))
          .toList() ?? [],
      unreadCommentsCount: json['unreadCommentsCount'] ?? 0,
      hasUnreadComments: json['hasUnreadComments'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'ticketNumber': ticketNumber,
      'title': title,
      'description': description,
      'status': status,
      'priority': priority,
      'category': category,
      'userId': userId,
      'userName': userName,
      'assignedToId': assignedToId,
      'assignedToName': assignedToName,
      'createdDate': createdDate.toIso8601String(),
      'updatedDate': updatedDate?.toIso8601String(),
      'assignedDate': assignedDate?.toIso8601String(),
      'resolvedDate': resolvedDate?.toIso8601String(),
      'closedDate': closedDate?.toIso8601String(),
      'lastStatusChangeDate': lastStatusChangeDate?.toIso8601String(),
      'previousStatus': previousStatus,
      'rollbackCount': rollbackCount,
      'lastRollbackDate': lastRollbackDate?.toIso8601String(),
      'comments': comments.map((comment) => comment.toJson()).toList(),
      'attachments': attachments.map((attachment) => attachment.toJson()).toList(),
      'unreadCommentsCount': unreadCommentsCount,
      'hasUnreadComments': hasUnreadComments,
    };
  }
}

class TicketComment {
  final int id;
  final int ticketId;
  final int userId;
  final String userName;
  final String? userAvatarUrl;
  final String content;
  final DateTime createdDate;
  final bool isInternal;

  TicketComment({
    required this.id,
    required this.ticketId,
    required this.userId,
    required this.userName,
    this.userAvatarUrl,
    required this.content,
    required this.createdDate,
    required this.isInternal,
  });

  factory TicketComment.fromJson(Map<String, dynamic> json) {
    return TicketComment(
      id: json['id'] ?? 0,
      ticketId: json['ticketId'] ?? 0,
      userId: json['userId'] ?? 0,
      userName: json['userName'] ?? '',
      userAvatarUrl: json['userAvatarUrl'],
      content: json['content'] ?? '',
      createdDate: DateTime.parse(json['createdDate']).toUtc(),
      isInternal: json['isInternal'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'ticketId': ticketId,
      'userId': userId,
      'userName': userName,
      'userAvatarUrl': userAvatarUrl,
      'content': content,
      'createdDate': createdDate.toIso8601String(),
      'isInternal': isInternal,
    };
  }
}

class TicketAttachment {
  final int id;
  final int ticketId;
  final String fileName;
  final String filePath;
  final int fileSize;
  final String contentType;
  final DateTime uploadedDate;

  TicketAttachment({
    required this.id,
    required this.ticketId,
    required this.fileName,
    required this.filePath,
    required this.fileSize,
    required this.contentType,
    required this.uploadedDate,
  });

  factory TicketAttachment.fromJson(Map<String, dynamic> json) {
    return TicketAttachment(
      id: json['id'] ?? 0,
      ticketId: json['ticketId'] ?? 0,
      fileName: json['fileName'] ?? '',
      filePath: json['filePath'] ?? '',
      fileSize: json['fileSize'] ?? 0,
      contentType: json['contentType'] ?? '',
      uploadedDate: DateTime.parse(json['uploadedDate']).toUtc(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'ticketId': ticketId,
      'fileName': fileName,
      'filePath': filePath,
      'fileSize': fileSize,
      'contentType': contentType,
      'uploadedDate': uploadedDate.toIso8601String(),
    };
  }
}

class CreateTicketRequest {
  final String title;
  final String description;
  final String priority;
  final String category;

  CreateTicketRequest({
    required this.title,
    required this.description,
    required this.priority,
    required this.category,
  });

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'priority': priority,
      'category': category,
    };
  }
}

class TicketStatus {
  static const String pending = 'Pending';
  static const String inProgress = 'InProgress';
  static const String resolved = 'Resolved';
  static const String closed = 'Closed';
}

class TicketPriority {
  static const String low = 'Low';
  static const String medium = 'Medium';
  static const String high = 'High';
}

class TicketCategory {
  static const String hardware = 'Hardware';
  static const String software = 'Software';
  static const String network = 'Network';
  static const String account = 'Account';
  static const String other = 'Other';
}

class TicketStatusLimits {
  final bool canChangeStatus;
  final bool canRollback;
  final int remainingRollbackCount;
  final Duration rollbackTimeRemaining;

  TicketStatusLimits({
    required this.canChangeStatus,
    required this.canRollback,
    required this.remainingRollbackCount,
    required this.rollbackTimeRemaining,
  });

  factory TicketStatusLimits.fromJson(Map<String, dynamic> json) {
    return TicketStatusLimits(
      canChangeStatus: json['canChangeStatus'] ?? false,
      canRollback: json['canRollback'] ?? false,
      remainingRollbackCount: json['remainingRollbackCount'] ?? 0,
      rollbackTimeRemaining: Duration(
        milliseconds: json['rollbackTimeRemainingMilliseconds']?.toInt() ?? 0,
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'canChangeStatus': canChangeStatus,
      'canRollback': canRollback,
      'remainingRollbackCount': remainingRollbackCount,
      'rollbackTimeRemainingMilliseconds': rollbackTimeRemaining.inMilliseconds,
    };
  }
}
