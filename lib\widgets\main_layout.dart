import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../config/routes.dart';
import '../providers/auth_provider.dart';
import '../providers/language_provider.dart';

class MainLayout extends StatefulWidget {
  final Widget child;
  final String currentRoute;
  final bool showBackButton;
  final String? title;
  final Widget? floatingActionButton;
  
  const MainLayout({
    super.key,
    required this.child,
    required this.currentRoute,
    this.showBackButton = false,
    this.title,
    this.floatingActionButton,
  });

  @override
  State<MainLayout> createState() => _MainLayoutState();
}

class _MainLayoutState extends State<MainLayout> {
  int _getCurrentIndex() {
    switch (widget.currentRoute) {
      case AppRoutes.dashboard:
        return 0;
      case AppRoutes.assetList:
        return 1;
      case AppRoutes.adminTicketList:
        return 2;
      case AppRoutes.activityLog:
        return 3;
      case '/users':
        return 4;
      default:
        // 对于子页面，根据父页面确定索引
        if (widget.currentRoute.startsWith('/assets')) {
          return 1; // 资产相关页面
        } else if (widget.currentRoute.startsWith('/admin/tickets')) {
          return 2; // 工单管理相关页面
        } else if (widget.currentRoute.startsWith('/users')) {
          return 4; // 用户管理相关页面
        }
        return 0; // 默认仪表板
    }
  }

  void _onTabTapped(int index) {
    switch (index) {
      case 0:
        context.go(AppRoutes.dashboard);
        break;
      case 1:
        context.go(AppRoutes.assetList);
        break;
      case 2:
        context.go(AppRoutes.adminTicketList);
        break;
      case 3:
        context.go(AppRoutes.activityLog);
        break;
      case 4:
        context.go('/users');
        break;
    }
  }

  String _getTitle(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    if (widget.title != null) {
      return widget.title!;
    }
    
    switch (widget.currentRoute) {
      case AppRoutes.dashboard:
        return l10n.dashboard;
      case AppRoutes.assetList:
        return l10n.assetManagement;
      case AppRoutes.adminTicketList:
        return l10n.ticketManagement;
      case AppRoutes.activityLog:
        return l10n.activityLog;
      case '/users':
        return l10n.userManagement;
      default:
        if (widget.currentRoute.startsWith('/assets')) {
          if (widget.currentRoute.contains('/new')) {
            return l10n.createAsset;
          } else if (widget.currentRoute.contains('/edit')) {
            return l10n.editAsset;
          } else if (widget.currentRoute.contains('/assets/') && !widget.currentRoute.contains('/edit')) {
            return l10n.assetDetails;
          }
          return l10n.assetManagement;
        } else if (widget.currentRoute.startsWith('/admin/tickets')) {
          if (widget.currentRoute.contains('/admin/tickets/') && !widget.currentRoute.contains('/assign')) {
            return l10n.ticketDetails;
          } else if (widget.currentRoute.contains('/assign')) {
            return l10n.assignTicket;
          }
          return l10n.ticketManagement;
        } else if (widget.currentRoute.startsWith('/users')) {
          if (widget.currentRoute.contains('/new')) {
            return l10n.createUser;
          } else if (widget.currentRoute.contains('/edit')) {
            return l10n.editUser;
          } else if (widget.currentRoute.contains('/users/') && !widget.currentRoute.contains('/edit')) {
            return l10n.userDetails;
          }
          return l10n.userManagement;
        }
        return l10n.itAssetManagement;
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(_getTitle(context)),
        leading: widget.showBackButton
            ? IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () {
                  if (Navigator.of(context).canPop()) {
                    Navigator.of(context).pop();
                  } else {
                    // 如果无法返回，根据当前页面跳转到合适的父页面
                    if (widget.currentRoute.startsWith('/assets')) {
                      context.go(AppRoutes.assetList);
                    } else if (widget.currentRoute.startsWith('/admin/tickets')) {
                      context.go(AppRoutes.adminTicketList);
                    } else if (widget.currentRoute.startsWith('/users')) {
                      context.go('/users');
                    } else {
                      context.go(AppRoutes.dashboard);
                    }
                  }
                },
              )
            : null,
        actions: [
          // 语言切换按钮
          Consumer<LanguageProvider>(
            builder: (context, languageProvider, child) {
              return IconButton(
                onPressed: () {
                  languageProvider.switchLanguage();
                },
                icon: const Icon(Icons.language),
                tooltip: l10n.switchLanguage,
              );
            },
          ),
          // 登出按钮
          IconButton(
            onPressed: () async {
              final confirmed = await showDialog<bool>(
                context: context,
                builder: (dialogContext) => AlertDialog(
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                  title: Row(
                    children: [
                      Icon(Icons.logout, color: Colors.orange[600]),
                      const SizedBox(width: 8),
                      Text(l10n.confirmLogout),
                    ],
                  ),
                  content: Text(l10n.confirmLogoutMessage),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(dialogContext).pop(false),
                      child: Text(
                        l10n.cancel,
                        style: TextStyle(color: Colors.grey[600]),
                      ),
                    ),
                    ElevatedButton(
                      onPressed: () => Navigator.of(dialogContext).pop(true),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange[600],
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(l10n.logout),
                    ),
                  ],
                ),
              );

              if (confirmed == true) {
                final authProvider = Provider.of<AuthProvider>(context, listen: false);
                await authProvider.logout();
                if (context.mounted) {
                  context.go(AppRoutes.login);
                }
              }
            },
            icon: const Icon(Icons.logout),
            tooltip: l10n.logoutTooltip,
          ),
        ],
      ),
      body: widget.child,
      floatingActionButton: widget.floatingActionButton,
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _getCurrentIndex(),
        onTap: _onTabTapped,
        type: BottomNavigationBarType.fixed,
        selectedItemColor: Theme.of(context).primaryColor,
        unselectedItemColor: Colors.grey,
        items: [
          BottomNavigationBarItem(
            icon: const Icon(Icons.dashboard),
            label: l10n.dashboard,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.inventory),
            label: l10n.assetManagement,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.support_agent),
            label: l10n.ticketManagement,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.history),
            label: l10n.activityLog,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.people),
            label: l10n.userManagement,
          ),
        ],
      ),
    );
  }
} 