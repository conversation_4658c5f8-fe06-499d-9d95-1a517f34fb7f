import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class TicketDisplayUtils {
  /// 获取工单状态的本地化显示文本
  static String getStatusDisplayText(String status, AppLocalizations l10n) {
    switch (status) {
      case 'Pending':
        return l10n.statusPending;
      case 'InProgress':
        return l10n.statusInProgress;
      case 'Resolved':
        return l10n.statusResolved;
      case 'Closed':
        return l10n.statusClosed;
      default:
        return status;
    }
  }

  /// 获取优先级的本地化显示文本
  static String getPriorityDisplayText(String priority, AppLocalizations l10n) {
    switch (priority) {
      case 'Low':
        return l10n.priorityLow;
      case 'Medium':
        return l10n.priorityMedium;
      case 'High':
        return l10n.priorityHigh;
      default:
        return priority;
    }
  }

  /// 获取分类的本地化显示文本
  static String getCategoryDisplayText(String category, AppLocalizations l10n) {
    switch (category) {
      case 'Hardware':
        return l10n.categoryHardware;
      case 'Software':
        return l10n.categorySoftware;
      case 'Network':
        return l10n.categoryNetwork;
      case 'Account':
        return l10n.categoryAccount;
      case 'Other':
        return l10n.categoryOther;
      default:
        return category;
    }
  }

  /// 获取状态对应的颜色
  static MaterialColor getStatusColor(String status) {
    switch (status) {
      case 'Pending':
        return Colors.orange;
      case 'InProgress':
        return Colors.blue;
      case 'Resolved':
        return Colors.green;
      case 'Closed':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  /// 获取优先级对应的颜色
  static MaterialColor getPriorityColor(String priority) {
    switch (priority) {
      case 'Low':
        return Colors.green;
      case 'Medium':
        return Colors.orange;
      case 'High':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  /// 获取状态对应的图标
  static IconData getStatusIcon(String status) {
    switch (status) {
      case 'Pending':
        return Icons.schedule;
      case 'InProgress':
        return Icons.play_arrow;
      case 'Resolved':
        return Icons.check_circle;
      case 'Closed':
        return Icons.close;
      default:
        return Icons.help_outline;
    }
  }

  /// 获取优先级对应的图标
  static IconData getPriorityIcon(String priority) {
    switch (priority) {
      case 'Low':
        return Icons.keyboard_arrow_down;
      case 'Medium':
        return Icons.remove;
      case 'High':
        return Icons.keyboard_arrow_up;
      default:
        return Icons.help_outline;
    }
  }

  /// 获取分类对应的图标
  static IconData getCategoryIcon(String category) {
    switch (category) {
      case 'Hardware':
        return Icons.computer;
      case 'Software':
        return Icons.apps;
      case 'Network':
        return Icons.wifi;
      case 'Account':
        return Icons.person;
      case 'Other':
        return Icons.more_horiz;
      default:
        return Icons.help_outline;
    }
  }
}
