import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../widgets/main_layout.dart';
import '../widgets/animated_search_bar.dart';
import '../config/routes.dart';
import '../models/ticket.dart';
import '../services/ticket_service.dart';
import '../utils/timezone_utils.dart';
import '../utils/ticket_display_utils.dart';

class AdminTicketListScreen extends StatefulWidget {
  const AdminTicketListScreen({super.key});

  @override
  State<AdminTicketListScreen> createState() => _AdminTicketListScreenState();
}

class _AdminTicketListScreenState extends State<AdminTicketListScreen> with TickerProviderStateMixin {
  final TicketService _ticketService = TicketService();
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  List<Ticket> _tickets = [];
  List<Ticket> _filteredTickets = [];
  bool _isLoading = true;
  String? _errorMessage;
  
  // 用于筛选选择的状态（未应用）
  List<String> _selectedStatuses = [];
  List<String> _selectedPriorities = [];
  List<String> _selectedCategories = [];
  String _searchQuery = '';
  
  // 已应用的筛选条件（用于实际筛选）
  List<String> _appliedStatuses = [];
  List<String> _appliedPriorities = [];
  List<String> _appliedCategories = [];
  String _appliedSearchText = '';
  
  bool _showBackToTop = false;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  final List<String> _statusOptions = ['Pending', 'InProgress', 'Resolved', 'Closed'];
  final List<String> _priorityOptions = ['Low', 'Medium', 'High'];
  final List<String> _categoryOptions = ['Hardware', 'Software', 'Network', 'Account', 'Other'];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _loadTickets();
    _searchController.addListener(_onSearchChanged);
    _scrollController.addListener(_onScrollChanged);
  }

  void _onScrollChanged() {
    final showBackToTop = _scrollController.offset > 200;
    if (showBackToTop != _showBackToTop) {
      setState(() {
        _showBackToTop = showBackToTop;
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
    });
  }

  void _applyFilters() {
    setState(() {
      _appliedStatuses = List.from(_selectedStatuses);
      _appliedPriorities = List.from(_selectedPriorities);
      _appliedCategories = List.from(_selectedCategories);
      _appliedSearchText = _searchQuery;
    });
    _filterTickets();
  }

  Future<void> _loadTickets() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // 使用管理员API获取带未读状态的所有工单
      final tickets = await _ticketService.getAllTicketsWithUnreadStatus();

      if (mounted) {
        setState(() {
          _tickets = tickets;
          _filteredTickets = tickets;
          _isLoading = false;
        });
        _animationController.forward();
        _filterTickets();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  void _filterTickets() {
    List<Ticket> filtered = _tickets;

    // Filter by status
    if (_appliedStatuses.isNotEmpty) {
      filtered = filtered.where((ticket) => _appliedStatuses.contains(ticket.status)).toList();
    }

    // Filter by priority
    if (_appliedPriorities.isNotEmpty) {
      filtered = filtered.where((ticket) => _appliedPriorities.contains(ticket.priority)).toList();
    }

    // Filter by category
    if (_appliedCategories.isNotEmpty) {
      filtered = filtered.where((ticket) => _appliedCategories.contains(ticket.category)).toList();
    }

    // Filter by search query
    if (_appliedSearchText.isNotEmpty) {
      filtered = filtered.where((ticket) {
        return ticket.title.toLowerCase().contains(_appliedSearchText.toLowerCase()) ||
               ticket.ticketNumber.toLowerCase().contains(_appliedSearchText.toLowerCase()) ||
               ticket.userName.toLowerCase().contains(_appliedSearchText.toLowerCase());
      }).toList();
    }

    setState(() {
      _filteredTickets = filtered;
    });
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: MainLayout(
        currentRoute: AppRoutes.adminTicketList,
        child: Stack(
          children: [
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.grey[50]!,
                    Colors.grey[100]!,
                  ],
                ),
              ),
              child: Column(
                children: [
                  _buildAdminToolbar(),
                  Expanded(
                    child: _buildTicketList(),
                  ),
                ],
              ),
            ),
            // 返回顶部按钮
            if (_showBackToTop)
              Positioned(
                right: 20,
                bottom: 100, // 避开底部导航栏
                child: _buildBackToTopButton(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackToTopButton() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.blue[600],
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withOpacity(0.3),
            spreadRadius: 0,
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            _scrollController.animateTo(
              0,
              duration: const Duration(milliseconds: 500),
              curve: Curves.easeInOut,
            );
          },
          borderRadius: BorderRadius.circular(16),
          child: Container(
            width: 48,
            height: 48,
            child: const Icon(
              Icons.keyboard_arrow_up_rounded,
              color: Colors.white,
              size: 24,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAdminToolbar() {
    final l10n = AppLocalizations.of(context)!;

    return Container(
      padding: const EdgeInsets.fromLTRB(20, 12, 20, 16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            spreadRadius: 0,
            blurRadius: 12,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // 搜索栏和筛选按钮并列
          Row(
            children: [
              // 动画搜索框
              Expanded(
                child: AnimatedSearchBar(
                  controller: _searchController,
                  hintText: l10n.searchTicketHint,
                  isSearching: _isLoading,
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                  onClear: () {
                    _searchController.clear();
                    setState(() {
                      _searchQuery = '';
                    });
                  },
                ),
              ),
              const SizedBox(width: 12),
              // 筛选按钮
              Container(
                decoration: BoxDecoration(
                  color: _hasAppliedFilters() ? Colors.blue[50] : Colors.grey[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: _hasAppliedFilters() ? Colors.blue[200]! : Colors.grey[200]!,
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.03),
                      spreadRadius: 0,
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Stack(
                  children: [
                    IconButton(
                      onPressed: _showFilterBottomSheet,
                      icon: Icon(
                        Icons.tune_rounded,
                        color: _hasAppliedFilters() ? Colors.blue[600] : Colors.grey[600],
                        size: 22,
                      ),
                      tooltip: l10n.filter,
                    ),
                    if (_hasAppliedFilters())
                      Positioned(
                        right: 8,
                        top: 8,
                        child: Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: Colors.red[500],
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
          
          // 条件性显示统计信息（只在有已应用的筛选条件时显示）
          if (_hasAppliedFilters())
            Container(
              margin: const EdgeInsets.only(top: 12),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue[100]!, width: 1),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Colors.blue[100],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.analytics_rounded,
                      color: Colors.blue[600],
                      size: 16,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      l10n.totalRecords(_filteredTickets.length),
                      style: TextStyle(
                        color: Colors.blue[700],
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  if (_isLoading)
                    SizedBox(
                      width: 18,
                      height: 18,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.blue[600]!),
                      ),
                    ),
                  const Spacer(),
                  // 清除筛选按钮
                  GestureDetector(
                    onTap: _clearFilters,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.blue[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.blue[200]!),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.clear_rounded,
                            color: Colors.blue[600],
                            size: 14,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            l10n.clearFilter,
                            style: TextStyle(
                              color: Colors.blue[600],
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  // 检查是否有已应用的筛选条件
  bool _hasAppliedFilters() {
    return _appliedStatuses.isNotEmpty ||
           _appliedPriorities.isNotEmpty ||
           _appliedCategories.isNotEmpty ||
           _appliedSearchText.isNotEmpty;
  }

  // 清除所有筛选条件
  void _clearFilters() {
    setState(() {
      _searchController.clear();
      _selectedStatuses.clear();
      _selectedPriorities.clear();
      _selectedCategories.clear();
      _appliedStatuses.clear();
      _appliedPriorities.clear();
      _appliedCategories.clear();
      _searchQuery = '';
      _appliedSearchText = '';
    });
    _filterTickets();
  }

  // 显示筛选底部弹窗
  void _showFilterBottomSheet() {
    final l10n = AppLocalizations.of(context)!;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => StatefulBuilder(
        builder: (context, setModalState) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
          ),
          padding: const EdgeInsets.fromLTRB(24, 20, 24, 32),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 顶部指示器
              Center(
                child: Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              const SizedBox(height: 20),
              // 标题
              Row(
                children: [
                  Icon(Icons.tune_rounded, color: Colors.grey[700], size: 24),
                  const SizedBox(width: 12),
                  Text(
                    l10n.filterConditions,
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w700,
                      color: Colors.grey[900],
                    ),
                  ),
                  const Spacer(),
                  if (_selectedStatuses.isNotEmpty || _selectedPriorities.isNotEmpty || _selectedCategories.isNotEmpty)
                    TextButton(
                      onPressed: () {
                        setState(() {
                          _selectedStatuses.clear();
                          _selectedPriorities.clear();
                          _selectedCategories.clear();
                        });
                        setModalState(() {});
                      },
                      child: Text(
                        l10n.clearAll,
                        style: TextStyle(
                          color: Colors.blue[600],
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 24),

              // 状态筛选
              Text(
                l10n.ticketStatus,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
              const SizedBox(height: 12),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  _buildMultiSelectChip(l10n.allStatuses, null, _selectedStatuses, (value) {
                    setState(() {
                      if (value == null) {
                        _selectedStatuses.clear();
                      }
                    });
                    setModalState(() {});
                  }),
                  ..._statusOptions.map((status) =>
                    _buildMultiSelectChip(
                      TicketDisplayUtils.getStatusDisplayText(status, l10n),
                      status,
                      _selectedStatuses,
                      (value) {
                        setState(() {
                          if (value != null) {
                            if (_selectedStatuses.contains(value)) {
                              _selectedStatuses.remove(value);
                            } else {
                              _selectedStatuses.add(value);
                            }
                          }
                        });
                        setModalState(() {});
                      }
                    )
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // 优先级筛选
              Text(
                l10n.priority,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
              const SizedBox(height: 12),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  _buildMultiSelectChip(l10n.allPriorities, null, _selectedPriorities, (value) {
                    setState(() {
                      if (value == null) {
                        _selectedPriorities.clear();
                      }
                    });
                    setModalState(() {});
                  }),
                  ..._priorityOptions.map((priority) =>
                    _buildMultiSelectChip(
                      TicketDisplayUtils.getPriorityDisplayText(priority, l10n),
                      priority,
                      _selectedPriorities,
                      (value) {
                        setState(() {
                          if (value != null) {
                            if (_selectedPriorities.contains(value)) {
                              _selectedPriorities.remove(value);
                            } else {
                              _selectedPriorities.add(value);
                            }
                          }
                        });
                        setModalState(() {});
                      }
                    )
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // 分类筛选
              Text(
                l10n.ticketCategory,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
              const SizedBox(height: 12),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  _buildMultiSelectChip(l10n.allCategories, null, _selectedCategories, (value) {
                    setState(() {
                      if (value == null) {
                        _selectedCategories.clear();
                      }
                    });
                    setModalState(() {});
                  }),
                  ..._categoryOptions.map((category) =>
                    _buildMultiSelectChip(
                      TicketDisplayUtils.getCategoryDisplayText(category, l10n),
                      category,
                      _selectedCategories,
                      (value) {
                        setState(() {
                          if (value != null) {
                            if (_selectedCategories.contains(value)) {
                              _selectedCategories.remove(value);
                            } else {
                              _selectedCategories.add(value);
                            }
                          }
                        });
                        setModalState(() {});
                      }
                    )
                  ),
                ],
              ),
              const SizedBox(height: 32),

              // 应用按钮
              SizedBox(
                width: double.infinity,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.blue[600]!, Colors.blue[700]!],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.blue.withOpacity(0.3),
                        spreadRadius: 0,
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: ElevatedButton(
                    onPressed: () {
                      _applyFilters();
                      Navigator.pop(context);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.transparent,
                      shadowColor: Colors.transparent,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                    ),
                    child: Text(
                      l10n.applyFilter,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 构建多选筛选chips（移除checkmark图标）
  Widget _buildMultiSelectChip(String label, String? value, List<String> selectedValues, Function(String?) onTap) {
    final isSelected = value == null ? selectedValues.isEmpty : selectedValues.contains(value);
    
    return GestureDetector(
      onTap: () => onTap(value),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue[600] : Colors.grey[100],
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? Colors.blue[600]! : Colors.grey[300]!,
          ),
          boxShadow: isSelected ? [
            BoxShadow(
              color: Colors.blue.withOpacity(0.3),
              spreadRadius: 0,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ] : null,
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.grey[700],
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
      ),
    );
  }





  Widget _buildTicketList() {
    final l10n = AppLocalizations.of(context)!;

    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            Text(
              l10n.loadingFailed,
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadTickets,
              child: Text(l10n.retry),
            ),
          ],
        ),
      );
    }

    if (_filteredTickets.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.assignment,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              l10n.noTickets,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              l10n.noTicketsUnderCurrentFilter,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: RefreshIndicator(
        onRefresh: _loadTickets,
        color: Colors.blue[600],
        child: ListView.builder(
          controller: _scrollController,
          padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
          itemCount: _filteredTickets.length,
          itemBuilder: (context, index) {
            final ticket = _filteredTickets[index];
            return AnimatedContainer(
              duration: Duration(milliseconds: 300 + (index * 50)),
              curve: Curves.easeOutBack,
              child: _buildAdminTicketCard(ticket, index),
            );
          },
        ),
      ),
    );
  }

  Widget _buildAdminTicketCard(Ticket ticket, int index) {
    final l10n = AppLocalizations.of(context)!;
    final priorityColor = TicketDisplayUtils.getPriorityColor(ticket.priority);
    final statusColor = TicketDisplayUtils.getStatusColor(ticket.status);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: ticket.hasUnreadComments ? Colors.blue[300]! : Colors.grey[200]!,
          width: ticket.hasUnreadComments ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: ticket.hasUnreadComments
                ? Colors.blue.withOpacity(0.1)
                : Colors.black.withOpacity(0.04),
            spreadRadius: 0,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          Material(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(12),
            child: InkWell(
              onTap: () {
                context.go('/admin/tickets/${ticket.id}');
              },
              borderRadius: BorderRadius.circular(12),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                // Header with ticket number, status and priority
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: priorityColor[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: priorityColor[200]!),
                      ),
                      child: Text(
                        ticket.ticketNumber,
                        style: TextStyle(
                          color: priorityColor[700],
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: statusColor[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: statusColor[200]!),
                      ),
                      child: Text(
                        TicketDisplayUtils.getStatusDisplayText(ticket.status, l10n),
                        style: TextStyle(
                          color: statusColor[700],
                          fontSize: 11,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    const Spacer(),
                    _buildPriorityChip(ticket.priority),
                  ],
                ),
                const SizedBox(height: 12),

                // Title
                Text(
                  ticket.title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[800],
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),

                // Description
                Text(
                  ticket.description,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 12),

                // User info and metadata
                Row(
                  children: [
                    Icon(
                      Icons.person,
                      size: 16,
                      color: Colors.grey[500],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      ticket.userName,
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Icon(
                      Icons.category,
                      size: 16,
                      color: Colors.grey[500],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      TicketDisplayUtils.getCategoryDisplayText(ticket.category, l10n),
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.grey[600],
                      ),
                    ),
                    const Spacer(),
                    Text(
                      _formatDateTime(ticket.createdDate),
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ),

                // Assignment info
                if (ticket.assignedToName != null) ...[
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(
                        Icons.assignment_ind,
                        size: 16,
                        color: Colors.green[600],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        l10n.assignedTo(ticket.assignedToName!),
                        style: TextStyle(
                          fontSize: 13,
                          color: Colors.green[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ],

                // Comments count
                if (ticket.comments.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(
                        Icons.comment,
                        size: 16,
                        color: Colors.blue[600],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        l10n.commentsCount(ticket.comments.length),
                        style: TextStyle(
                          fontSize: 13,
                          color: Colors.blue[600],
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ),
        ),
        // 未读评论徽章
        if (ticket.hasUnreadComments)
          Positioned(
            top: 8,
            right: 8,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.red[600],
                borderRadius: BorderRadius.circular(10),
                boxShadow: [
                  BoxShadow(
                    color: Colors.red.withOpacity(0.3),
                    spreadRadius: 0,
                    blurRadius: 4,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Text(
                '${ticket.unreadCommentsCount}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
      ],
    ),
    );
  }

  Widget _buildPriorityChip(String priority) {
    final l10n = AppLocalizations.of(context)!;
    final color = TicketDisplayUtils.getPriorityColor(priority);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color[200]!),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            TicketDisplayUtils.getPriorityIcon(priority),
            size: 12,
            color: color[700],
          ),
          const SizedBox(width: 4),
          Text(
            TicketDisplayUtils.getPriorityDisplayText(priority, l10n),
            style: TextStyle(
              color: color[700],
              fontSize: 11,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }



  String _formatDateTime(DateTime dateTime) {
    final l10n = AppLocalizations.of(context)!;
    return TimezoneUtils.formatRelativeTime(dateTime, l10n);
  }
}
