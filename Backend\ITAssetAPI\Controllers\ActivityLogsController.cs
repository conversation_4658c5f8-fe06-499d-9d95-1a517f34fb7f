using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ITAssetAPI.DTOs;
using ITAssetAPI.Services;
using ITAssetAPI.Models;

namespace ITAssetAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ActivityLogsController : ControllerBase
    {
        private readonly IActivityLogService _activityLogService;
        private readonly IExcelService _excelService;

        public ActivityLogsController(IActivityLogService activityLogService, IExcelService excelService)
        {
            _activityLogService = activityLogService;
            _excelService = excelService;
        }

        [HttpGet]
        public async Task<ActionResult<ActivityLogListResponseDto>> GetActivityLogs(
            [FromQuery] int page = 1,
            [FromQuery] int limit = 20,
            [FromQuery] string? search = null,
            [FromQuery] string? activityType = null, // 支持逗号分隔的多个类型
            [FromQuery] int? assetId = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            try
            {
                Console.WriteLine($"=== ActivityLogsController.GetActivityLogs ===");
                Console.WriteLine($"Received activityType: '{activityType}'");
                Console.WriteLine($"Received startDate: {startDate}");
                Console.WriteLine($"Received endDate: {endDate}");
                Console.WriteLine($"startDate HasValue: {startDate.HasValue}");
                Console.WriteLine($"endDate HasValue: {endDate.HasValue}");
                
                var result = await _activityLogService.GetActivityLogsAsync(page, limit, search, activityType, assetId, startDate, endDate);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Internal server error", details = ex.Message });
            }
        }

        [HttpPost]
        public async Task<ActionResult<ActivityLogDto>> CreateActivityLog([FromBody] CreateActivityLogDto createActivityLogDto)
        {
            try
            {
                var result = await _activityLogService.CreateActivityLogAsync(createActivityLogDto);
                return CreatedAtAction(nameof(GetActivityLogs), new { id = result.Id }, result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Internal server error", details = ex.Message });
            }
        }

        [HttpGet("export")]
        public async Task<ActionResult> ExportActivityLogsToExcel(
            [FromQuery] string? search = null,
            [FromQuery] string? activityType = null, // 支持逗号分隔的多个类型
            [FromQuery] int? assetId = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            try
            {
                // 获取所有符合条件的活动日志（不分页）
                var allLogs = await _activityLogService.GetActivityLogsAsync(1, int.MaxValue, search, activityType, assetId, startDate, endDate);
                
                // 转换为ActivityLog模型列表
                var activityLogs = allLogs.ActivityLogs.Select(dto => new ActivityLog
                {
                    Id = dto.Id,
                    ActivityType = dto.ActivityType,
                    Description = dto.Description,
                    AssetId = dto.AssetId,
                    UserId = dto.UserId,
                    UserName = dto.UserName,
                    AssetName = dto.AssetName,
                    AssetNumber = dto.AssetNumber,
                    OldValues = dto.OldValues,
                    NewValues = dto.NewValues,
                    CreatedAt = dto.CreatedAt
                }).ToList();

                var excelData = await _excelService.ExportActivityLogsToExcelAsync(activityLogs);
                
                var fileName = $"活动日志_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                return File(excelData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Internal server error", details = ex.Message });
            }
        }
    }
} 