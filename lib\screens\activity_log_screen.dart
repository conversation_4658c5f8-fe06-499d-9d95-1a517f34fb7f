import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/activity_log.dart';
import '../services/api_service.dart';
import '../services/excel_service.dart';
import '../widgets/main_layout.dart';
import '../widgets/compact_search_bar.dart';
import '../widgets/animated_search_bar.dart';
import '../widgets/scroll_to_top_button.dart';
import '../widgets/success_animation.dart';
import '../config/routes.dart';
import '../utils/timezone_utils.dart';

class ActivityLogScreen extends StatefulWidget {
  const ActivityLogScreen({super.key});

  @override
  State<ActivityLogScreen> createState() => _ActivityLogScreenState();
}

class _ActivityLogScreenState extends State<ActivityLogScreen> {
  final ApiService _apiService = ApiService();
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  Timer? _searchTimer; // 添加搜索定时器
  
  List<ActivityLog> _activityLogs = [];
  bool _isLoading = false;
  String? _error;
  int _currentPage = 1;
  int _totalPages = 1;
  int _totalCount = 0;
  final int _limit = 20;
  
  List<String> _selectedActivityTypes = []; // 改为列表支持多选
  int? _selectedAssetId;
  DateTime? _startDate;
  DateTime? _endDate;

  // 已应用的筛选条件（用于实际筛选和显示统计）
  List<String> _appliedActivityTypes = []; // 改为列表支持多选
  DateTime? _appliedStartDate;
  DateTime? _appliedEndDate;
  String _appliedSearchText = '';

  final List<String> _activityTypes = [
    'Create',
    'Update', 
    'Delete',
    'Assign',
    'Unassign',
    'Maintenance',
  ];

  @override
  void initState() {
    super.initState();
    _loadActivityLogs();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    _searchTimer?.cancel(); // 取消搜索定时器
    super.dispose();
  }

  Future<void> _loadActivityLogs() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      print('=== 加载活动日志 ===');
      print('页码: $_currentPage, 限制: $_limit');
      print('搜索: ${_searchController.text}');
      print('活动类型: $_selectedActivityTypes');
      print('已应用活动类型: $_appliedActivityTypes');
      print('开始日期: $_startDate');
      print('结束日期: $_endDate');
      
      final activityTypeParam = _appliedActivityTypes.isNotEmpty ? _appliedActivityTypes : null;
      print('传递给API的活动类型参数: $activityTypeParam');
      
      final response = await _apiService.getActivityLogs(
        page: _currentPage,
        limit: _limit,
        search: _appliedSearchText.isNotEmpty ? _appliedSearchText : null,
        activityType: activityTypeParam,
        assetId: _selectedAssetId,
        startDate: _appliedStartDate,
        endDate: _appliedEndDate,
      );

      print('响应成功，日志数量: ${response.activityLogs.length}');
      
      setState(() {
        _activityLogs = response.activityLogs;
        _totalPages = response.totalPages;
        _totalCount = response.totalCount;
        _isLoading = false;
      });
    } catch (e) {
      print('加载活动日志失败: $e');
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  void _onSearch() {
    setState(() {
      _appliedSearchText = _searchController.text;
    });
    _currentPage = 1;
    _loadActivityLogs();
  }

  // 新增：实时搜索文本变化处理
  void _onSearchTextChanged(String value) {
    // 取消之前的定时器
    _searchTimer?.cancel();

    // 设置新的定时器，500毫秒后执行搜索
    _searchTimer = Timer(const Duration(milliseconds: 500), () {
      setState(() {
        _appliedSearchText = value;
      });
      _currentPage = 1;
      _loadActivityLogs();
    });
  }

  void _onSearchChanged() {
    // 取消之前的搜索定时器
    _searchTimer?.cancel();
    
    // 设置新的搜索定时器，延迟搜索避免频繁请求
    _searchTimer = Timer(const Duration(milliseconds: 500), () {
      if (mounted) {
        _onSearch();
      }
    });
  }

  void _onFilterChanged() {
    _currentPage = 1;
    _loadActivityLogs();
  }

  void _clearFilters() {
    setState(() {
      _searchController.clear();
      _selectedActivityTypes = [];
      _selectedAssetId = null;
      _startDate = null;
      _endDate = null;
      // 清除已应用的筛选条件
      _appliedActivityTypes = [];
      _appliedStartDate = null;
      _appliedEndDate = null;
      _appliedSearchText = '';
      _currentPage = 1;
    });
    _loadActivityLogs();
  }

  void _goToPage(int page) {
    if (page >= 1 && page <= _totalPages) {
      setState(() {
        _currentPage = page;
      });
      _loadActivityLogs();
    }
  }

  Future<void> _exportToExcel() async {
    // 显示保存位置选择对话框
    final saveToDownload = await _showSaveLocationDialog();
    if (saveToDownload == null) return; // 用户取消了选择
    
    try {
      // 显示加载对话框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在导出Excel文件...'),
            ],
          ),
        ),
      );

      // 获取所有活动日志数据
      final activityLogs = await _apiService.getAllActivityLogs(
        search: _appliedSearchText.isNotEmpty ? _appliedSearchText : null,
        activityType: _appliedActivityTypes.isNotEmpty ? _appliedActivityTypes : null,
        assetId: _selectedAssetId,
        startDate: _appliedStartDate,
        endDate: _appliedEndDate,
      );

      // 导出Excel
      final filePath = await ExcelService.exportActivityLogsToExcel(
        activityLogs,
        saveToDownload: saveToDownload,
      );
      
      // 关闭加载对话框
      if (mounted) {
        Navigator.of(context).pop();

        // 显示现代化的成功动画
        SuccessAnimationOverlay.show(
          context,
          title: '导出成功',
          message: 'Excel文件已保存到: $filePath',
          onComplete: () {
            // 成功动画完成后的回调
          },
        );
      }
    } catch (e) {
      // 关闭加载对话框
      if (mounted) {
        Navigator.of(context).pop();
        
        // 显示错误消息
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('导出失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<bool?> _showSaveLocationDialog() async {
    return showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('选择保存位置'),
          content: const Text('请选择Excel文件的保存位置：'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(null),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('应用目录'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Download文件夹'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return MainLayout(
      currentRoute: AppRoutes.activityLog,
      child: Stack(
        children: [
          Column(
            children: [
              // 现代化工具栏
              Container(
                padding: const EdgeInsets.fromLTRB(20, 12, 20, 16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.03),
                      spreadRadius: 0,
                      blurRadius: 12,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: _buildToolbar(),
              ),


          
          // 条件性显示统计信息（只在有已应用的筛选条件时显示）
          if (_hasAppliedFilters())
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue[100]!, width: 1),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Colors.blue[100],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.analytics_rounded,
                      color: Colors.blue[600],
                      size: 16,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      '共 $_totalCount 条记录',
                      style: TextStyle(
                        color: Colors.blue[700],
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  if (_isLoading)
                    SizedBox(
                      width: 18,
                      height: 18,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.blue[600]!),
                      ),
                    ),
                  const Spacer(),
                  // 清除筛选按钮（完全复制资产列表样式）
                  GestureDetector(
                    onTap: _clearFilters,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.blue[50],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.clear_rounded,
                            color: Colors.blue[600],
                            size: 14,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '清除',
                            style: TextStyle(
                              color: Colors.blue[600],
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),

          // 活动日志列表
          Expanded(
            child: _buildContent(),
          ),
            ],
          ),
          
          // 回到顶部按钮
          Positioned(
            right: 16,
            bottom: 16,
            child: ScrollToTopButton(
              scrollController: _scrollController,
              showOffset: 200.0,
              backgroundColor: Theme.of(context).primaryColor,
              iconColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading && _activityLogs.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[300],
            ),
            const SizedBox(height: 16),
            Text(
              '加载失败',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: TextStyle(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadActivityLogs,
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (_activityLogs.isEmpty) {
      return Center(
        child: Container(
          margin: const EdgeInsets.all(40),
          padding: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: Colors.grey[100]!, width: 1),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.04),
                spreadRadius: 0,
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(
                  Icons.history_rounded,
                  size: 64,
                  color: Colors.grey[400],
                ),
              ),
              const SizedBox(height: 24),
              Text(
                '暂无活动日志',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w700,
                  color: Colors.grey[800],
                  letterSpacing: -0.5,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '当前没有符合条件的活动记录',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadActivityLogs,
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(16),
        itemCount: _activityLogs.length,
        itemBuilder: (context, index) {
          final log = _activityLogs[index];
          return _buildActivityLogCard(log);
        },
      ),
    );
  }

  Widget _buildActivityLogCard(ActivityLog log) {
    // 严格去重：确保主活动类型和次要活动类型不重复
    final secondaryTypes = log.secondaryActivityTypes
        .where((type) => type != log.activityType)
        .toSet()
        .toList();
    
    // 更严格的重复记录过滤逻辑
    bool shouldShow = true;
    
    // 如果是纯Update记录（没有次要类型或只有Update次要类型）
    if (log.activityType == ActivityType.update && 
        (secondaryTypes.isEmpty || (secondaryTypes.length == 1 && secondaryTypes[0] == ActivityType.update))) {
      
      // 检查是否有变更详情，如果没有变更详情则隐藏
      if (log.oldValues == null || log.newValues == null) {
        shouldShow = false;
      } else {
        try {
          Map<String, dynamic> oldValues = jsonDecode(log.oldValues!);
          Map<String, dynamic> newValues = jsonDecode(log.newValues!);
          
          // 检查是否有实际变更
          bool hasActualChanges = false;
          for (var key in newValues.keys) {
            if (oldValues.containsKey(key) && oldValues[key].toString() != newValues[key].toString()) {
              hasActualChanges = true;
              break;
            }
          }
          
          if (!hasActualChanges) {
            shouldShow = false;
          }
        } catch (e) {
          // 如果解析失败，也隐藏这条记录
          shouldShow = false;
        }
      }
      
      // 额外检查：如果前一条记录是同一资产的非Update操作，且时间相近，则隐藏
      if (shouldShow) {
        final currentIndex = _activityLogs.indexOf(log);
        if (currentIndex > 0) {
          final previousLog = _activityLogs[currentIndex - 1];
          if (previousLog.assetId == log.assetId && 
              previousLog.activityType != ActivityType.update &&
              previousLog.createdAt.difference(log.createdAt).inMinutes.abs() < 2) {
            shouldShow = false;
          }
        }
      }
    }
    
    if (!shouldShow) {
      return const SizedBox.shrink(); // 不显示重复或无意义的更新记录
    }
        
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey[100]!, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            spreadRadius: 0,
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.02),
            spreadRadius: 0,
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: ExpansionTile(
          tilePadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
          childrenPadding: const EdgeInsets.only(left: 20, right: 20, bottom: 20),
          initiallyExpanded: false,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 现代化标题设计
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _getActivityTypeColor(log.activityType).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    _getActivityTypeIcon(log.activityType),
                    color: _getActivityTypeColor(log.activityType),
                    size: 18,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 活动ID
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(color: Colors.grey[300]!, width: 1),
                        ),
                        child: Text(
                          'LOG${log.id.toString().padLeft(6, '0')}',
                          style: TextStyle(
                            fontSize: 11,
                            fontWeight: FontWeight.w600,
                            color: Colors.grey[700],
                            fontFamily: 'monospace',
                          ),
                        ),
                      ),
                      const SizedBox(height: 6),
                      // 标题
                      Text(
                        _buildSimpleTitle(log),
                        style: const TextStyle(
                          fontWeight: FontWeight.w700,
                          fontSize: 16,
                          color: Colors.black87,
                          letterSpacing: -0.2,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // 现代化活动类型标签
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                // 显示主活动类型标签
                _buildModernActivityTypeChip(log.activityType),

                // 显示次要活动类型标签 (去重后的)
                ...secondaryTypes.map((type) =>
                  _buildModernActivityTypeChip(type, isSecondary: true)
                ),
              ],
            ),
            
            // 现代化日期和用户信息
            const SizedBox(height: 8),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.access_time_rounded,
                        size: 14,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        TimezoneUtils.formatDateTime(log.createdAt),
                        style: TextStyle(
                          color: Colors.grey[700],
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue[100]!, width: 1),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.person_rounded,
                        size: 14,
                        color: Colors.blue[600],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        log.userName,
                        style: TextStyle(
                          color: Colors.blue[700],
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            // 如果有变更摘要，显示简短的变更内容
            if (log.changeSummary != null && log.changeSummary!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: Colors.blue[100]!),
                ),
                child: Text(
                  log.changeSummary!,
                  style: TextStyle(
                    color: Colors.blue[800],
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ],
        ),
        subtitle: null,
        children: [
          // 抽屉内容：详细信息
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Divider(height: 1),
              const SizedBox(height: 12),
              
              // 详细描述
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '详细描述',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 6),
                  Text(
                    log.description,
                    style: const TextStyle(
                      fontSize: 13,
                      height: 1.4,
                    ),
                  ),
                ],
              ),
              
              // 资产信息（如果有）
              if (log.assetName != null || log.assetNumber != null) ...[
                const SizedBox(height: 12),
                const Text(
                  '资产信息',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 6),
                if (log.assetName != null) 
                  _buildInfoRow('资产名称', log.assetName!),
                if (log.assetNumber != null) ...[
                  const SizedBox(height: 4),
                  _buildInfoRow('资产编号', log.assetNumber!),
                ],
              ],

              // 变更详情（如果有）
              if (log.oldValues != null || log.newValues != null) ...[
                const SizedBox(height: 12),
                const Text(
                  '变更详情',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 6),
                _buildChangesTable(log.oldValues, log.newValues),
              ],
            ],
          ),
        ],
        ),
      ),
    );
  }

  // 新增：构建变更表格
  Widget _buildChangesTable(String? oldValuesJson, String? newValuesJson) {
    if (oldValuesJson == null || newValuesJson == null) {
      return const Text('无变更详情');
    }
    
    try {
      Map<String, dynamic> oldValues = jsonDecode(oldValuesJson);
      Map<String, dynamic> newValues = jsonDecode(newValuesJson);
      
      List<TableRow> rows = [];
      
      // 添加表头
      rows.add(
        TableRow(
          decoration: BoxDecoration(
            color: Colors.grey[200],
          ),
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                '字段',
                style: TextStyle(fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                '旧值',
                style: TextStyle(fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                '新值',
                style: TextStyle(fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      );
      
      // 收集所有字段名
      Set<String> allFields = {};
      allFields.addAll(oldValues.keys);
      allFields.addAll(newValues.keys);
      
      // 按照优先级排序字段
      List<String> priorityFields = [
        'Status', 'AssignedTo', 'Category', 'Name', 'Brand', 'Model'
      ];
      
      List<String> sortedFields = [];
      // 先添加优先级字段
      for (var field in priorityFields) {
        if (allFields.contains(field)) {
          sortedFields.add(field);
          allFields.remove(field);
        }
      }
      // 再添加剩余字段
      sortedFields.addAll(allFields);
      
      // 创建表格行
      for (var field in sortedFields) {
        String oldValue = oldValues[field]?.toString() ?? '无';
        String newValue = newValues[field]?.toString() ?? '无';
        
        // 只显示有变化的字段
        if (oldValue != newValue) {
          rows.add(
            TableRow(
              decoration: BoxDecoration(
                color: Colors.white,
              ),
              children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(_getFieldDisplayName(field)),
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    _getDisplayValue(field, oldValue),
                    style: TextStyle(
                      color: Colors.red[700],
                      decoration: TextDecoration.lineThrough,
                      decorationColor: Colors.red[300],
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    _getDisplayValue(field, newValue),
                    style: TextStyle(
                      color: Colors.green[700],
                    ),
                  ),
                ),
              ],
            ),
          );
        }
      }
      
      if (rows.length == 1) {  // 只有表头，没有数据行
        return const Text('无变更详情');
      }
      
      return Table(
        border: TableBorder.all(
          color: Colors.grey[300]!,
          width: 1,
        ),
        columnWidths: const {
          0: FlexColumnWidth(1.5),
          1: FlexColumnWidth(2),
          2: FlexColumnWidth(2),
        },
        children: rows,
      );
    } catch (e) {
      print('Error parsing JSON: $e');
      return Text('解析变更详情失败: $e');
    }
  }

  // 新增：获取字段显示名称
  String _getFieldDisplayName(String fieldName) {
    switch (fieldName) {
      case 'Status':
        return '状态';
      case 'AssignedTo':
        return '分配给';
      case 'AssignedUserId':
        return '分配用户ID';
      case 'Category':
        return '分类';
      case 'Name':
        return '名称';
      case 'Brand':
        return '品牌';
      case 'Model':
        return '型号';
      case 'Location':
        return '位置';
      case 'Value':
        return '价值';
      case 'Description':
        return '描述';
      case 'SerialNumber':
        return '序列号';
      default:
        return fieldName;
    }
  }

  // 新增：获取显示值
  String _getDisplayValue(String field, String value) {
    if (value.isEmpty || value == 'null') return '无';
    
    // 处理状态枚举
    if (field == 'Status') {
      switch (value) {
        case 'Available':
          return '可用';
        case 'Assigned':
          return '已分配';
        case 'Maintenance':
          return '维护中';
        case 'Retired':
          return '已报废';
        default:
          return value;
      }
    }
    
    // 处理分类枚举
    if (field == 'Category') {
      switch (value) {
        case 'Laptop':
          return '笔记本电脑';
        case 'Desktop':
          return '台式电脑';
        case 'Monitor':
          return '显示器';
        case 'Printer':
          return '打印机';
        case 'Phone':
          return '电话';
        case 'Tablet':
          return '平板电脑';
        case 'Server':
          return '服务器';
        case 'Network':
          return '网络设备';
        case 'Other':
          return '其他';
        default:
          return value;
      }
    }
    
    return value;
  }

  // 修改：增强活动类型标签
  Widget _buildActivityTypeChip(ActivityType type, {bool isSecondary = false}) {
    final color = _getActivityTypeColor(type);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
      decoration: BoxDecoration(
        color: isSecondary ? color.withOpacity(0.25) : color.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSecondary ? color.withOpacity(0.7) : color.withOpacity(0.8),
          width: isSecondary ? 1.2 : 1.5,
        ),
      ),
      child: Text(
        ActivityLog.getActivityTypeDisplayName(type),
        style: TextStyle(
          fontSize: isSecondary ? 11 : 12,
          fontWeight: isSecondary ? FontWeight.w600 : FontWeight.bold,
          color: isSecondary ? color.withOpacity(0.9) : color,
        ),
      ),
    );
  }

  // 新增：获取活动类型颜色
  Color _getActivityTypeColor(ActivityType type) {
    switch (type) {
      case ActivityType.create:
        return Colors.green[700]!;
      case ActivityType.update:
        return Colors.blue[700]!;
      case ActivityType.delete:
        return Colors.red[700]!;
      case ActivityType.assign:
        return Colors.orange[700]!;
      case ActivityType.unassign:
        return Colors.amber[700]!;
      case ActivityType.maintenance:
        return Colors.indigo[700]!;
      case ActivityType.dispose:
        return Colors.purple[700]!;
      default:
        return Colors.grey[700]!;
    }
  }

  // 修改：生成简洁标题的方法
  String _buildSimpleTitle(ActivityLog log) {
    // 优先使用后端生成的描述，如果描述已经包含了完整信息
    if (log.description.isNotEmpty && log.description != '创建了资产 ${log.assetName} (${log.assetNumber})') {
      // 如果描述包含"并分配给"或"被分配给"等关键词，直接使用描述
      if (log.description.contains('分配给') || log.description.contains('取消分配') || log.description.contains('维护') || log.description.contains('报废')) {
        return log.description;
      }
    }
    
    // 使用资产名称和编号构建基础信息
    String assetInfo = '';
    if (log.assetName != null) {
      assetInfo = log.assetName!;
      if (log.assetNumber != null) {
        assetInfo += ' (${log.assetNumber})';
      }
    } else if (log.assetNumber != null) {
      assetInfo = log.assetNumber!;
    } else {
      assetInfo = '资产';
    }
    
    // 根据不同活动类型生成不同的简洁标题
    switch (log.activityType) {
      case ActivityType.create:
        // 检查是否有分配信息
        if (log.secondaryActivityTypes.contains(ActivityType.assign)) {
          if (log.newValues != null) {
            try {
              Map<String, dynamic> newValues = jsonDecode(log.newValues!);
              String assignedTo = newValues['AssignedTo'] ?? '';
              if (assignedTo.isNotEmpty && assignedTo != 'null') {
                return '创建了$assetInfo并分配给$assignedTo';
              }
            } catch (e) {
              print('Error parsing newValues in create: $e');
            }
          }
        }
        return '创建了$assetInfo';
        
      case ActivityType.update:
        return '更新了$assetInfo';
        
      case ActivityType.delete:
        return '删除了$assetInfo';
        
      case ActivityType.assign:
        if (log.newValues != null) {
          try {
            Map<String, dynamic> newValues = jsonDecode(log.newValues!);
            String assignedTo = newValues['AssignedTo'] ?? '';
            if (assignedTo.isNotEmpty && assignedTo != 'null') {
              return '$assetInfo被分配给$assignedTo';
            }
          } catch (e) {
            print('Error parsing assignedTo: $e');
          }
        }
        // 从描述中提取分配信息
        if (log.description.contains('分配给')) {
          final parts = log.description.split('分配给');
          if (parts.length > 1) {
            final assignee = parts[1].split('并')[0].trim();
            return '$assetInfo被分配给$assignee';
          }
        }
        return '$assetInfo被分配';
        
      case ActivityType.unassign:
        if (log.oldValues != null) {
          try {
            Map<String, dynamic> oldValues = jsonDecode(log.oldValues!);
            String assignedTo = oldValues['AssignedTo'] ?? '';
            if (assignedTo.isNotEmpty && assignedTo != 'null') {
              return '$assetInfo从$assignedTo取消分配';
            }
          } catch (e) {
            print('Error parsing oldValues: $e');
          }
        }
        // 从描述中提取取消分配信息
        if (log.description.contains('从')) {
          final parts = log.description.split('从');
          if (parts.length > 1) {
            final assigneeParts = parts[1].split('取消分配');
            if (assigneeParts.length > 0) {
              final assignee = assigneeParts[0].trim();
              return '$assetInfo从$assignee取消分配';
            }
          }
        }
        return '$assetInfo取消分配';
        
      case ActivityType.maintenance:
        return '$assetInfo设为维护中';
        
      case ActivityType.dispose:
        return '$assetInfo设为已报废';
        
      default:
        return log.description;
    }
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: Text(
            '$label:',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 12,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPagination() {
    return Container(
      margin: const EdgeInsets.all(20),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey[100]!, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            spreadRadius: 0,
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 上一页按钮
          Container(
            decoration: BoxDecoration(
              color: _currentPage > 1 ? Colors.blue[50] : Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: _currentPage > 1 ? Colors.blue[200]! : Colors.grey[200]!,
                width: 1,
              ),
            ),
            child: IconButton(
              onPressed: _currentPage > 1 ? () => _goToPage(_currentPage - 1) : null,
              icon: Icon(
                Icons.chevron_left_rounded,
                color: _currentPage > 1 ? Colors.blue[600] : Colors.grey[400],
                size: 24,
              ),
              tooltip: '上一页',
            ),
          ),
          // 页码信息
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[200]!, width: 1),
            ),
            child: Text(
              '第 $_currentPage 页，共 $_totalPages 页',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
            ),
          ),
          // 下一页按钮
          Container(
            decoration: BoxDecoration(
              color: _currentPage < _totalPages ? Colors.blue[50] : Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: _currentPage < _totalPages ? Colors.blue[200]! : Colors.grey[200]!,
                width: 1,
              ),
            ),
            child: IconButton(
              onPressed: _currentPage < _totalPages ? () => _goToPage(_currentPage + 1) : null,
              icon: Icon(
                Icons.chevron_right_rounded,
                color: _currentPage < _totalPages ? Colors.blue[600] : Colors.grey[400],
                size: 24,
              ),
              tooltip: '下一页',
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _selectStartDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _startDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _startDate) {
      print('=== 选择开始日期 ===');
      print('选择的日期: $picked');
      setState(() {
        _startDate = picked;
        // 如果开始日期晚于结束日期，清除结束日期
        if (_endDate != null && picked.isAfter(_endDate!)) {
          _endDate = null;
          print('清除结束日期，因为开始日期晚于结束日期');
        }
      });
      print('调用 _onFilterChanged()');
      _onFilterChanged();
    }
  }

  Future<void> _selectEndDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _endDate ?? (_startDate ?? DateTime.now()),
      firstDate: _startDate ?? DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _endDate) {
      print('=== 选择结束日期 ===');
      print('选择的日期: $picked');
      setState(() {
        _endDate = picked;
      });
      print('调用 _onFilterChanged()');
      _onFilterChanged();
    }
  }

  String _getActivityTypeDisplayName(String type) {
    switch (type.toLowerCase()) {
      case 'create':
        return '创建';
      case 'update':
        return '更新';
      case 'delete':
        return '删除';
      case 'assign':
        return '分配';
      case 'unassign':
        return '取消分配';
      case 'maintenance':
        return '维护';
      default:
        return type;
    }
  }

  // 新增：现代化活动类型标签
  Widget _buildModernActivityTypeChip(ActivityType type, {bool isSecondary = false}) {
    final color = _getActivityTypeColor(type);
    final icon = _getActivityTypeIcon(type);
    final displayName = ActivityLog.getActivityTypeDisplayName(type);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: isSecondary ? color.withOpacity(0.08) : color.withOpacity(0.12),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSecondary ? color.withOpacity(0.3) : color.withOpacity(0.4),
          width: 1
        ),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: color,
            size: isSecondary ? 12 : 14
          ),
          const SizedBox(width: 6),
          Text(
            displayName,
            style: TextStyle(
              color: color,
              fontSize: isSecondary ? 11 : 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  // 新增：获取活动类型图标
  IconData _getActivityTypeIcon(ActivityType type) {
    switch (type) {
      case ActivityType.create:
        return Icons.add_circle_rounded;
      case ActivityType.update:
        return Icons.edit_rounded;
      case ActivityType.delete:
        return Icons.delete_rounded;
      case ActivityType.assign:
        return Icons.person_add_rounded;
      case ActivityType.unassign:
        return Icons.person_remove_rounded;
      case ActivityType.maintenance:
        return Icons.build_rounded;
      case ActivityType.dispose:
        return Icons.delete_forever_rounded;
      default:
        return Icons.info_rounded;
    }
  }

  // 新增：构建工具栏
  Widget _buildToolbar() {
    return Row(
      children: [
        // 动画搜索框
        Expanded(
          child: AnimatedSearchBar(
            controller: _searchController,
            hintText: '搜索活动ID、标题关键词...',
            isSearching: _isLoading,
            onChanged: (value) {
              setState(() {});
              _onSearchTextChanged(value);
            },
            onClear: () {
              _searchController.clear();
              _onSearch();
            },
          ),
        ),
        const SizedBox(width: 12),
        // 筛选按钮
        Container(
          decoration: BoxDecoration(
            color: _hasAppliedFilters() ? Colors.blue[50] : Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: _hasAppliedFilters() ? Colors.blue[200]! : Colors.grey[200]!,
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.03),
                spreadRadius: 0,
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Stack(
            children: [
              IconButton(
                onPressed: _showFilterBottomSheet,
                icon: Icon(
                  Icons.tune_rounded,
                  color: _hasAppliedFilters() ? Colors.blue[600] : Colors.grey[600],
                  size: 22,
                ),
                tooltip: '筛选',
              ),
              if (_hasAppliedFilters())
                Positioned(
                  right: 8,
                  top: 8,
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: Colors.red[500],
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
            ],
          ),
        ),
        const SizedBox(width: 8),
        // 导出按钮
        Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.green[600]!, Colors.green[700]!],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.green.withOpacity(0.3),
                spreadRadius: 0,
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: IconButton(
            onPressed: _exportToExcel,
            icon: const Icon(Icons.download_rounded, color: Colors.white, size: 22),
            tooltip: '导出Excel',
          ),
        ),
      ],
    );
  }

  // 新增：检查是否有活动筛选条件（不包括搜索）
  bool _hasActiveFilters() {
    return _selectedActivityTypes.isNotEmpty ||
           _startDate != null ||
           _endDate != null;
  }

  // 新增：检查是否有已应用的筛选条件（用于显示统计信息）
  bool _hasAppliedFilters() {
    return _appliedActivityTypes.isNotEmpty ||
           _appliedStartDate != null ||
           _appliedEndDate != null ||
           _appliedSearchText.isNotEmpty;
  }

  // 新增：显示筛选底部弹窗
  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => StatefulBuilder(
        builder: (context, setModalState) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 标题栏
                Row(
                  children: [
                    Icon(Icons.tune_rounded, color: Colors.grey[700], size: 24),
                    const SizedBox(width: 12),
                    Text(
                      '筛选条件',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w700,
                        color: Colors.grey[900],
                      ),
                    ),
                    const Spacer(),
                    if (_hasActiveFilters())
                      TextButton(
                        onPressed: () {
                          setState(() {
                            _selectedActivityTypes = [];
                            _startDate = null;
                            _endDate = null;
                            _searchController.clear();
                            // 清除已应用的筛选条件
                            _appliedActivityTypes = [];
                            _appliedStartDate = null;
                            _appliedEndDate = null;
                            _appliedSearchText = '';
                          });
                          setModalState(() {});
                          _onFilterChanged();
                          Navigator.pop(context);
                        },
                        child: Text(
                          '清除',
                          style: TextStyle(
                            color: Colors.blue[600],
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 24),

                // 活动类型筛选
                Text(
                  '活动类型',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[800],
                  ),
                ),
                const SizedBox(height: 12),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: [
                    _buildFilterChip('全部类型', null, _selectedActivityTypes, (newSelection) {
                      setState(() {
                        _selectedActivityTypes = newSelection;
                      });
                      setModalState(() {});
                    }),
                    ..._activityTypes.map((type) =>
                      _buildFilterChip(
                        _getActivityTypeDisplayName(type),
                        type,
                        _selectedActivityTypes,
                        (newSelection) {
                          setState(() {
                            _selectedActivityTypes = newSelection;
                          });
                          setModalState(() {});
                        }
                      )
                    ),
                  ],
                ),
                const SizedBox(height: 24),

                // 日期范围筛选
                Text(
                  '日期范围',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[800],
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _buildDateSelector(
                        '开始日期',
                        _startDate,
                        () async {
                          await _selectStartDate(context);
                          setModalState(() {}); // 更新弹窗状态
                        },
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildDateSelector(
                        '结束日期',
                        _endDate,
                        () async {
                          await _selectEndDate(context);
                          setModalState(() {}); // 更新弹窗状态
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),

                // 确认按钮
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      // 应用筛选条件
                      setState(() {
                        _appliedActivityTypes = _selectedActivityTypes;
                        _appliedStartDate = _startDate;
                        _appliedEndDate = _endDate;
                      });
                      _onFilterChanged();
                      Navigator.pop(context);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue[600],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      '应用筛选',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 新增：构建筛选芯片（支持多选）
  Widget _buildFilterChip(String label, String? value, List<String> selectedValues, Function(List<String>) onTap) {
    final isSelected = value == null ? selectedValues.isEmpty : selectedValues.contains(value);

    return GestureDetector(
      onTap: () {
        List<String> newSelection = List.from(selectedValues);
        if (value == null) {
          // "全部类型" 被点击，清空所有选择
          newSelection.clear();
        } else {
          // 具体类型被点击，切换选择状态
          if (newSelection.contains(value)) {
            newSelection.remove(value);
          } else {
            newSelection.add(value);
          }
        }
        onTap(newSelection);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue[600] : Colors.grey[100],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? Colors.blue[600]! : Colors.grey[200]!,
            width: 1,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.grey[700],
            fontSize: 14,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
          ),
        ),
      ),
    );
  }

  // 新增：构建日期选择器
  Widget _buildDateSelector(String label, DateTime? date, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[200]!, width: 1),
        ),
        child: Row(
          children: [
            Icon(
              Icons.calendar_today_rounded,
              size: 16,
              color: Colors.grey[600],
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                date != null
                    ? DateFormat('MM/dd').format(date)
                    : label,
                style: TextStyle(
                  fontSize: 14,
                  color: date != null ? Colors.black87 : Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}