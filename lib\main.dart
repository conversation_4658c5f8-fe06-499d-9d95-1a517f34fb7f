import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'config/theme.dart';
import 'config/routes.dart';
import 'providers/auth_provider.dart';
import 'providers/asset_provider.dart';
import 'providers/dashboard_provider.dart';
import 'providers/language_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 初始化语言设置
  final languageProvider = LanguageProvider();
  await languageProvider.initializeLanguage();
  
  runApp(ITAssetApp(languageProvider: languageProvider));
}

class ITAssetApp extends StatefulWidget {
  final LanguageProvider languageProvider;
  
  const ITAssetApp({super.key, required this.languageProvider});

  @override
  State<ITAssetApp> createState() => _ITAssetAppState();
}

class _ITAssetAppState extends State<ITAssetApp> {
  late final AuthProvider _authProvider;
  late final GoRouter _router;
  
  @override
  void initState() {
    super.initState();
    // 创建AuthProvider和路由器，避免重复创建
    _authProvider = AuthProvider();
    _router = createAppRouter(_authProvider);
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider.value(value: widget.languageProvider),
        ChangeNotifierProvider.value(value: _authProvider),
        ChangeNotifierProvider(create: (_) => AssetProvider()),
        ChangeNotifierProvider(create: (_) => DashboardProvider()),
      ],
      child: Consumer<LanguageProvider>(
        builder: (context, languageProvider, _) {
          return MaterialApp.router(
            title: 'IT Asset App',
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: ThemeMode.system,
            routerConfig: _router,
            debugShowCheckedModeBanner: false,
            localizationsDelegates: const [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: const [
              Locale('zh'),
              Locale('en'),
            ],
            locale: languageProvider.currentLocale,
          );
        },
      ),
    );
  }
}
