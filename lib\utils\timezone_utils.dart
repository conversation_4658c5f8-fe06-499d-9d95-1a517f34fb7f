import 'package:intl/intl.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

/// 时区工具类 - 专门处理马来西亚时区（UTC+8）
class TimezoneUtils {
  // 马来西亚时区偏移量（UTC+8）
  static const int malaysiaOffsetHours = 8;
  
  /// 将UTC时间转换为马来西亚时间
  static DateTime toMalaysiaTime(DateTime utcDateTime) {
    // 确保输入是UTC时间
    final utc = utcDateTime.isUtc ? utcDateTime : utcDateTime.toUtc();
    // 添加8小时偏移量
    return utc.add(Duration(hours: malaysiaOffsetHours));
  }
  
  /// 格式化为马来西亚时间的日期时间字符串
  static String formatDateTime(DateTime dateTime) {
    final malaysiaTime = toMalaysiaTime(dateTime);
    return '${malaysiaTime.year}-${malaysiaTime.month.toString().padLeft(2, '0')}-${malaysiaTime.day.toString().padLeft(2, '0')} ${malaysiaTime.hour.toString().padLeft(2, '0')}:${malaysiaTime.minute.toString().padLeft(2, '0')}';
  }
  
  /// 格式化为马来西亚时间的日期字符串
  static String formatDate(DateTime dateTime) {
    final malaysiaTime = toMalaysiaTime(dateTime);
    return '${malaysiaTime.year}-${malaysiaTime.month.toString().padLeft(2, '0')}-${malaysiaTime.day.toString().padLeft(2, '0')}';
  }
  
  /// 格式化为相对时间（几分钟前、几小时前等）
  static String formatRelativeTime(DateTime dateTime, [AppLocalizations? l10n]) {
    // 获取当前本地时间
    final nowLocal = DateTime.now();

    // 将输入的UTC时间转换为本地时间进行比较
    final inputLocal = dateTime.isUtc ? dateTime.toLocal() : dateTime;

    final difference = nowLocal.difference(inputLocal);

    // 如果没有提供国际化对象，返回硬编码的中文（向后兼容）
    if (l10n == null) {
      if (difference.inDays > 0) {
        return '${difference.inDays}天前';
      } else if (difference.inMinutes >= 60) {
        final hours = (difference.inMinutes / 60).floor();
        return '${hours}小时前';
      } else if (difference.inMinutes > 0) {
        return '${difference.inMinutes}分钟前';
      } else {
        return '刚刚';
      }
    }

    // 使用国际化
    if (difference.inMinutes < 1) {
      return l10n.justNow;
    } else if (difference.inHours < 1) {
      return l10n.minutesAgo(difference.inMinutes);
    } else if (difference.inDays < 1) {
      return l10n.hoursAgo(difference.inHours);
    } else if (difference.inDays < 7) {
      return l10n.daysAgo(difference.inDays);
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return l10n.weeksAgo(weeks);
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return l10n.monthsAgo(months);
    } else {
      final years = (difference.inDays / 365).floor();
      return l10n.yearsAgo(years);
    }
  }
  
  /// 使用intl包格式化时间（支持更复杂的格式）
  static String formatWithPattern(DateTime dateTime, String pattern) {
    final malaysiaTime = toMalaysiaTime(dateTime);
    final formatter = DateFormat(pattern);
    return formatter.format(malaysiaTime);
  }
  
  /// 获取当前马来西亚时间
  static DateTime nowInMalaysia() {
    final utcNow = DateTime.now().toUtc();
    return toMalaysiaTime(utcNow);
  }
  
  /// 调试用：显示时间转换信息
  static void debugTimeConversion(DateTime originalTime) {
    print('=== 时间转换调试 ===');
    print('原始时间: $originalTime');
    print('是否UTC: ${originalTime.isUtc}');
    print('转换为UTC: ${originalTime.toUtc()}');
    print('转换为本地: ${originalTime.toLocal()}');
    print('马来西亚时间: ${toMalaysiaTime(originalTime)}');
    print('格式化结果: ${formatDateTime(originalTime)}');
    print('相对时间: ${formatRelativeTime(originalTime)}'); // 使用默认中文版本
    print('==================');
  }
}
